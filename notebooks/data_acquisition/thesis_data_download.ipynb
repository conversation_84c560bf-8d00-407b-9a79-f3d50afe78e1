import os
from pathlib import Path
from datetime import datetime

# Setup data directory
DATA_DIR = Path("../../data/thesis_datasets/pointcloud")
DATA_DIR.mkdir(parents=True, exist_ok=True)

print(f"Data directory: {DATA_DIR.absolute()}")
print(f"Download started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Download Castro Area4 (7 GB) using rclone
!rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las ../../data/thesis_datasets/pointcloud/ --progress

# Verify Castro download
castro_file = Path("../../data/thesis_datasets/pointcloud/area4_point.las")
if castro_file.exists():
    size_gb = castro_file.stat().st_size / (1024**3)
    print(f"Castro Area4 downloaded: {size_gb:.1f} GB")
else:
    print("Castro Area4 download failed")

# Download RPCS Point Cloud (1.3 GB) using rclone
!rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las ../../data/thesis_datasets/pointcloud/ --progress

# Verify RPCS download
rpcs_file = Path("../../data/thesis_datasets/pointcloud/Point_Cloud.las")
if rpcs_file.exists():
    size_gb = rpcs_file.stat().st_size / (1024**3)
    print(f"RPCS Point Cloud downloaded: {size_gb:.1f} GB")
else:
    print("RPCS Point Cloud download failed")

# Download McCarthy Buffer (36 MB) using rclone
!rclone copy "datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las" ../../data/thesis_datasets/pointcloud/ --progress

# Verify McCarthy download
mccarthy_file = Path("../../data/thesis_datasets/pointcloud/Buffer_las(rev1).las")
if mccarthy_file.exists():
    size_mb = mccarthy_file.stat().st_size / (1024**2)
    print(f"McCarthy Buffer downloaded: {size_mb:.1f} MB")
else:
    print("McCarthy Buffer download failed")

# Check all downloads
import pandas as pd

datasets = [
    {'name': 'Castro Area4', 'file': 'area4_point.las', 'expected_gb': 7.0},
    {'name': 'RPCS Point Cloud', 'file': 'Point_Cloud.las', 'expected_gb': 1.3},
    {'name': 'McCarthy Buffer', 'file': 'Buffer_las(rev1).las', 'expected_gb': 0.036}
]

results = []
total_size = 0

for dataset in datasets:
    file_path = Path(f"../../data/thesis_datasets/pointcloud/{dataset['file']}")
    
    if file_path.exists():
        actual_size_gb = file_path.stat().st_size / (1024**3)
        status = "Downloaded"
        total_size += actual_size_gb
    else:
        actual_size_gb = 0
        status = "Failed"
    
    results.append({
        'Dataset': dataset['name'],
        'Expected_GB': dataset['expected_gb'],
        'Actual_GB': round(actual_size_gb, 2),
        'Status': status
    })

summary_df = pd.DataFrame(results)
print("DOWNLOAD SUMMARY:")
print(summary_df)
print(f"\nTotal downloaded: {total_size:.1f} GB")

# Check thesis readiness
downloaded_count = len([r for r in results if r['Status'] == 'Downloaded'])

print("THESIS READINESS ASSESSMENT:")
print("=" * 40)

if downloaded_count >= 2:
    print(f"READY FOR THESIS WORK!")
    print(f"- {downloaded_count} datasets available")
    print(f"- {total_size:.1f} GB total data")
    print(f"- Sufficient for comparative analysis")
    
    print(f"\nNEXT STEPS:")
    print(f"1. Start method implementation on primary dataset")
    print(f"2. Validate methods work on single dataset")
    print(f"3. Expand to cross-validation")
    print(f"4. Perform statistical comparison")
    
elif downloaded_count == 1:
    print(f"PARTIAL SUCCESS")
    print(f"- 1 dataset available")
    print(f"- Can start development")
    print(f"- Limited cross-validation")
    
else:
    print(f"DOWNLOAD ISSUES")
    print(f"- No datasets downloaded")
    print(f"- Check AWS credentials")
    print(f"- Retry downloads")

print(f"\nDownload completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Final validation
print("\n🔍 THESIS READINESS VALIDATION")
print("=" * 40)

total_downloaded_size = 0
ready_for_thesis = True

for key in successful_downloads:
    dataset = DATASETS[key]
    local_path = DATA_DIR / "pointcloud" / dataset['local_filename']
    
    if local_path.exists():
        actual_size_gb = local_path.stat().st_size / (1024**3)
        total_downloaded_size += actual_size_gb
        
        print(f"✅ {dataset['name']}:")
        print(f"   File: {local_path}")
        print(f"   Size: {actual_size_gb:.2f} GB")
        print(f"   Purpose: {dataset['purpose']}")
        print()

# Check minimum requirements
if len(successful_downloads) >= 2:
    print(f"🎉 THESIS READY!")
    print(f"   ✅ {len(successful_downloads)} datasets available")
    print(f"   ✅ {total_downloaded_size:.1f} GB total data")
    print(f"   ✅ Sufficient for comparative analysis")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. Start method implementation on primary dataset")
    print(f"   2. Validate methods work on single dataset")
    print(f"   3. Expand to cross-validation on secondary datasets")
    print(f"   4. Perform statistical comparison analysis")
    
elif len(successful_downloads) == 1:
    print(f"⚠️ PARTIAL SUCCESS")
    print(f"   ✅ 1 dataset available - can start development")
    print(f"   ⚠️ Limited cross-validation capability")
    print(f"   💡 Consider downloading additional datasets later")
    
else:
    print(f"❌ THESIS RISK")
    print(f"   ❌ No datasets successfully downloaded")
    print(f"   🔧 Check AWS credentials and network connectivity")
    print(f"   🔄 Retry download or consider alternative datasets")
    ready_for_thesis = False

# Export dataset manifest
if successful_downloads:
    manifest = {
        'download_date': datetime.now().isoformat(),
        'total_size_gb': total_downloaded_size,
        'datasets': {}
    }
    
    for key in successful_downloads:
        dataset = DATASETS[key]
        local_path = DATA_DIR / "pointcloud" / dataset['local_filename']
        manifest['datasets'][key] = {
            'name': dataset['name'],
            'local_path': str(local_path),
            'size_gb': local_path.stat().st_size / (1024**3),
            'purpose': dataset['purpose']
        }
    
    import json
    manifest_path = DATA_DIR / "dataset_manifest.json"
    with open(manifest_path, 'w') as f:
        json.dump(manifest, f, indent=2)
    
    print(f"\n📄 Dataset manifest saved: {manifest_path}")

print(f"\n📅 Download completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")