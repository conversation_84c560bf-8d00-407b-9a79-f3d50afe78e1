{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Thesis Data Acquisition\n", "\n", "**Purpose**: Download optimal datasets identified in the assessment phase\n", "\n", "**Target Datasets**: \n", "- Castro Area4 (7 GB) - Primary development dataset\n", "- RPCS Point Cloud (1.3 GB) - Cross-validation dataset  \n", "- <PERSON> (36 MB) - Quick testing dataset\n", "\n", "**Total Size**: ~8.3 GB (manageable for thesis timeline)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import subprocess\n", "import os\n", "from pathlib import Path\n", "import time\n", "from datetime import datetime\n", "\n", "# Configuration\n", "DATA_DIR = Path(\"../../data/thesis_datasets\")\n", "DATA_DIR.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"📁 Data will be downloaded to: {DATA_DIR.absolute()}\")\n", "print(f\"📅 Download started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Dataset Configuration\n", "\n", "Optimal datasets selected from assessment analysis:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Thesis-optimal datasets\n", "DATASETS = {\n", "    'castro_area4': {\n", "        'name': 'Castro Area4',\n", "        'url': 's3://preetam-filezilla-test/Castro/Pointcloud/area4_point.las',\n", "        'size_gb': 7.0,\n", "        'priority': 1,\n", "        'purpose': 'Primary development dataset',\n", "        'local_filename': 'castro_area4.las'\n", "    },\n", "    'rpcs_main': {\n", "        'name': 'RPCS Point Cloud',\n", "        'url': 's3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las',\n", "        'size_gb': 1.3,\n", "        'priority': 2,\n", "        'purpose': 'Cross-validation dataset',\n", "        'local_filename': 'rpcs_pointcloud.las'\n", "    },\n", "    'mccarthy_buffer': {\n", "        'name': '<PERSON>',\n", "        'url': 's3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las',\n", "        'size_gb': 0.036,\n", "        'priority': 3,\n", "        'purpose': 'Quick testing dataset',\n", "        'local_filename': 'mccarthy_buffer.las'\n", "    }\n", "}\n", "\n", "# Display dataset plan\n", "print(\"🎯 THESIS DATASET DOWNLOAD PLAN\")\n", "print(\"=\" * 50)\n", "total_size = 0\n", "for key, dataset in DATASETS.items():\n", "    print(f\"{dataset['priority']}. {dataset['name']} ({dataset['size_gb']} GB)\")\n", "    print(f\"   Purpose: {dataset['purpose']}\")\n", "    print(f\"   Output: {dataset['local_filename']}\")\n", "    total_size += dataset['size_gb']\n", "    print()\n", "\n", "print(f\"📦 Total Size: {total_size:.1f} GB\")\n", "print(f\"⏱️ Estimated Time: {total_size * 2:.0f} minutes\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Download Functions\n", "\n", "Simple, reliable download utilities:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def download_s3_file(s3_url, local_path, dataset_name):\n", "    \"\"\"Download a single file from S3 using AWS CLI\"\"\"\n", "    \n", "    print(f\"\\n📥 Downloading {dataset_name}...\")\n", "    print(f\"   Source: {s3_url}\")\n", "    print(f\"   Destination: {local_path}\")\n", "    \n", "    # Ensure directory exists\n", "    local_path.parent.mkdir(parents=True, exist_ok=True)\n", "    \n", "    try:\n", "        start_time = time.time()\n", "        \n", "        # Use AWS CLI for reliable download\n", "        result = subprocess.run([\n", "            'aws', 's3', 'cp', s3_url, str(local_path), '--progress'\n", "        ], check=True, capture_output=False)\n", "        \n", "        end_time = time.time()\n", "        duration = end_time - start_time\n", "        \n", "        # Verify download\n", "        if local_path.exists():\n", "            size_mb = local_path.stat().st_size / (1024**2)\n", "            speed_mbps = size_mb / duration if duration > 0 else 0\n", "            \n", "            print(f\"   ✅ Success: {size_mb:.1f} MB in {duration:.1f}s ({speed_mbps:.1f} MB/s)\")\n", "            return True\n", "        else:\n", "            print(f\"   ❌ Failed: File not created\")\n", "            return False\n", "            \n", "    except subprocess.CalledProcessError as e:\n", "        print(f\"   ❌ Download failed: {e}\")\n", "        return False\n", "    except Exception as e:\n", "        print(f\"   ❌ Unexpected error: {e}\")\n", "        return False\n", "\n", "def verify_dataset(local_path, expected_size_gb, dataset_name):\n", "    \"\"\"Verify downloaded dataset integrity\"\"\"\n", "    \n", "    if not local_path.exists():\n", "        print(f\"   ❌ {dataset_name}: File missing\")\n", "        return False\n", "    \n", "    actual_size_gb = local_path.stat().st_size / (1024**3)\n", "    size_diff_pct = abs(actual_size_gb - expected_size_gb) / expected_size_gb * 100\n", "    \n", "    if size_diff_pct < 10:  # Allow 10% variance\n", "        print(f\"   ✅ {dataset_name}: {actual_size_gb:.2f} GB (verified)\")\n", "        return True\n", "    else:\n", "        print(f\"   ⚠️ {dataset_name}: {actual_size_gb:.2f} GB (expected {expected_size_gb:.2f} GB)\")\n", "        return True  # Still usable, just different size than expected"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Download Execution\n", "\n", "Sequential download of thesis datasets:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Download datasets in priority order\n", "download_results = {}\n", "successful_downloads = []\n", "\n", "print(\"🚀 STARTING THESIS DATA DOWNLOAD\")\n", "print(\"=\" * 40)\n", "\n", "for key, dataset in sorted(DATASETS.items(), key=lambda x: x[1]['priority']):\n", "    dataset_name = dataset['name']\n", "    s3_url = dataset['url']\n", "    local_filename = dataset['local_filename']\n", "    expected_size = dataset['size_gb']\n", "    \n", "    # Create local path\n", "    local_path = DATA_DIR / \"pointcloud\" / local_filename\n", "    \n", "    # Skip if already exists\n", "    if local_path.exists():\n", "        print(f\"\\n⏭️ {dataset_name}: Already exists, skipping download\")\n", "        if verify_dataset(local_path, expected_size, dataset_name):\n", "            successful_downloads.append(key)\n", "        continue\n", "    \n", "    # Download dataset\n", "    success = download_s3_file(s3_url, local_path, dataset_name)\n", "    download_results[key] = success\n", "    \n", "    if success:\n", "        if verify_dataset(local_path, expected_size, dataset_name):\n", "            successful_downloads.append(key)\n", "    \n", "    # Brief pause between downloads\n", "    time.sleep(2)\n", "\n", "print(f\"\\n📋 DOWNLOAD SUMMARY\")\n", "print(\"=\" * 30)\n", "print(f\"Successful downloads: {len(successful_downloads)}/{len(DATASETS)}\")\n", "for key in successful_downloads:\n", "    dataset = DATASETS[key]\n", "    print(f\"   ✅ {dataset['name']} ({dataset['size_gb']} GB)\")\n", "\n", "failed_downloads = set(DATASETS.keys()) - set(successful_downloads)\n", "if failed_downloads:\n", "    print(f\"\\nFailed downloads:\")\n", "    for key in failed_downloads:\n", "        dataset = DATASETS[key]\n", "        print(f\"   ❌ {dataset['name']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Dataset Validation\n", "\n", "Verify thesis readiness:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Final validation\n", "print(\"\\n🔍 THESIS READINESS VALIDATION\")\n", "print(\"=\" * 40)\n", "\n", "total_downloaded_size = 0\n", "ready_for_thesis = True\n", "\n", "for key in successful_downloads:\n", "    dataset = DATASETS[key]\n", "    local_path = DATA_DIR / \"pointcloud\" / dataset['local_filename']\n", "    \n", "    if local_path.exists():\n", "        actual_size_gb = local_path.stat().st_size / (1024**3)\n", "        total_downloaded_size += actual_size_gb\n", "        \n", "        print(f\"✅ {dataset['name']}:\")\n", "        print(f\"   File: {local_path}\")\n", "        print(f\"   Size: {actual_size_gb:.2f} GB\")\n", "        print(f\"   Purpose: {dataset['purpose']}\")\n", "        print()\n", "\n", "# Check minimum requirements\n", "if len(successful_downloads) >= 2:\n", "    print(f\"🎉 THESIS READY!\")\n", "    print(f\"   ✅ {len(successful_downloads)} datasets available\")\n", "    print(f\"   ✅ {total_downloaded_size:.1f} GB total data\")\n", "    print(f\"   ✅ Sufficient for comparative analysis\")\n", "    \n", "    print(f\"\\n🚀 NEXT STEPS:\")\n", "    print(f\"   1. Start method implementation on primary dataset\")\n", "    print(f\"   2. Validate methods work on single dataset\")\n", "    print(f\"   3. Expand to cross-validation on secondary datasets\")\n", "    print(f\"   4. Perform statistical comparison analysis\")\n", "    \n", "elif len(successful_downloads) == 1:\n", "    print(f\"⚠️ PARTIAL SUCCESS\")\n", "    print(f\"   ✅ 1 dataset available - can start development\")\n", "    print(f\"   ⚠️ Limited cross-validation capability\")\n", "    print(f\"   💡 Consider downloading additional datasets later\")\n", "    \n", "else:\n", "    print(f\"❌ THESIS RISK\")\n", "    print(f\"   ❌ No datasets successfully downloaded\")\n", "    print(f\"   🔧 Check AWS credentials and network connectivity\")\n", "    print(f\"   🔄 Retry download or consider alternative datasets\")\n", "    ready_for_thesis = False\n", "\n", "# Export dataset manifest\n", "if successful_downloads:\n", "    manifest = {\n", "        'download_date': datetime.now().isoformat(),\n", "        'total_size_gb': total_downloaded_size,\n", "        'datasets': {}\n", "    }\n", "    \n", "    for key in successful_downloads:\n", "        dataset = DATASETS[key]\n", "        local_path = DATA_DIR / \"pointcloud\" / dataset['local_filename']\n", "        manifest['datasets'][key] = {\n", "            'name': dataset['name'],\n", "            'local_path': str(local_path),\n", "            'size_gb': local_path.stat().st_size / (1024**3),\n", "            'purpose': dataset['purpose']\n", "        }\n", "    \n", "    import json\n", "    manifest_path = DATA_DIR / \"dataset_manifest.json\"\n", "    with open(manifest_path, 'w') as f:\n", "        json.dump(manifest, f, indent=2)\n", "    \n", "    print(f\"\\n📄 Dataset manifest saved: {manifest_path}\")\n", "\n", "print(f\"\\n📅 Download completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON>\n", "\n", "**Data Acquisition Results:**\n", "- Systematic download of thesis-optimal datasets\n", "- Verification of data integrity and accessibility\n", "- Preparation for method development phase\n", "\n", "**Academic Value:**\n", "- Reproducible data acquisition process\n", "- Clear documentation of dataset selection rationale\n", "- Foundation for rigorous comparative analysis\n", "\n", "**Next Phase:**\n", "- Begin method implementation on primary dataset\n", "- Establish baseline performance metrics\n", "- Prepare for cross-validation analysis"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}