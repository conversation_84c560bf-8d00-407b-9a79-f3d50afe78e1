{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project Data Assessment for Thesis\n", "\n", "**Purpose**: Evaluate available point cloud datasets using direct AWS CLI commands\n", "\n", "**Thesis Context**: 12-week timeline requires manageable dataset sizes\n", "\n", "**Methodology**: Direct AWS CLI exploration + pandas analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "# Thesis constraints\n", "THESIS_SIZE_LIMIT_GB = 10\n", "OPTIMAL_SIZE_RANGE = (0.5, 5.0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Project Inventory\n", "\n", "Complete list of available projects with point cloud locations:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Project inventory\n", "projects_data = {\n", "    'Project': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', 'RPCS', 'RES'],\n", "    'Full_Name': [\n", "        '<PERSON><PERSON><PERSON> - ENEL',\n", "        'Mudjar - ENEL', \n", "        '<PERSON><PERSON> - ENEL',\n", "        'Sunstreams Project - McCarthy',\n", "        'Althea - RPCS',\n", "        'Nortan - RES Renewables'\n", "    ],\n", "    'URL_Type': ['S3_Folder', 'HTTPS_Direct', 'HTTPS_Direct', 'S3_Folder', 'S3_Folder', 'S3_Folder'],\n", "    'Estimated_Size_GB': [46.8, 1.4, 2.8, 2.1, 1.8, 15.7]\n", "}\n", "\n", "df = pd.DataFrame(projects_data)\n", "print(\"Project Inventory:\")\n", "print(df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Castro Point Cloud Analysis\n", "\n", "Check Castro folder contents (known to have 4 large LAS files):"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Castro - Check S3 folder contents\n", "!aws s3 ls s3://preetam-filezilla-test/Castro/Pointcloud/ --human-readable"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# <PERSON><PERSON> results manually\n", "castro_files = [\n", "    {'filename': 'area4_point.las', 'size_gb': 7.0},\n", "    {'filename': 'Area1_part1_point.las', 'size_gb': 15.2},\n", "    {'filename': 'Area2_point.las', 'size_gb': 10.7},\n", "    {'filename': 'Area1_Part2_Points.las', 'size_gb': 13.9}\n", "]\n", "\n", "castro_total = sum(f['size_gb'] for f in castro_files)\n", "print(f\"Castro total: {castro_total} GB\")\n", "print(\"Files:\")\n", "for f in castro_files:\n", "    print(f\"  {f['filename']}: {f['size_gb']} GB\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. RPCS Point Cloud Analysis\n", "\n", "Check RPCS folder contents:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RPCS - Check S3 folder contents\n", "!aws s3 ls s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/ --human-readable"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. McCarthy Point Cloud Analysis\n", "\n", "Check McCarthy folder contents:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# McCarthy - Check S3 folder contents\n", "!aws s3 ls s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/ --human-readable"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. RES Point Cloud Analysis\n", "\n", "Check RES folder contents:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RES - Check S3 folder contents\n", "!aws s3 ls \"s3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/\" --human-readable"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Mudjar Point Cloud Analysis\n", "\n", "Check Mudjar HTTPS file accessibility:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Mudjar - Check HTTPS file accessibility\n", "!curl -I --max-time 30 \"https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Giorgio Point Cloud Analysis\n", "\n", "Check Giorgio HTTPS file accessibility:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Giorgio - Check HTTPS file accessibility\n", "!curl -I --max-time 30 \"https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>/Flight12/<PERSON>_Fly12_pointcloud.las\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Assessment Matrix Creation\n", "\n", "Create comprehensive assessment based on exploration results:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create assessment matrix based on exploration results\n", "assessment_data = {\n", "    'Project': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "    'Actual_Size_GB': [46.8, 1.3, 0.036, 15.7, 0, 0],  # Based on AWS CLI results\n", "    'File_Count': [4, 2, 1, 1, 0, 0],\n", "    'Accessible': [True, True, True, True, False, False],\n", "    'Key_Files': [\n", "        'area4_point.las (7GB), Area1_part1_point.las (15.2GB), Area2_point.las (10.7GB), Area1_Part2_Points.las (13.9GB)',\n", "        'Point_Cloud.las (1.3GB), Point_Cloud_0.5.las (113.5MB)',\n", "        'Buffer_las(rev1).las (36.1MB)',\n", "        'Block_11.las (15.7GB)',\n", "        'HTTP 403 Forbidden',\n", "        'HTTP request failed'\n", "    ]\n", "}\n", "\n", "assessment_df = pd.DataFrame(assessment_data)\n", "print(\"Assessment Matrix:\")\n", "print(assessment_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Thesis Suitability Analysis\n", "\n", "Apply thesis constraints and generate recommendations:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add thesis suitability analysis\n", "def assess_suitability(size_gb, accessible):\n", "    if not accessible:\n", "        return \"Not Accessible\", \"None\", \"Skip - Not accessible\"\n", "    elif size_gb == 0:\n", "        return \"No Data\", \"None\", \"Skip - No data found\"\n", "    elif size_gb <= 0.1:\n", "        return \"Very Small\", \"Low\", \"Warning - May lack sufficient data\"\n", "    elif size_gb <= OPTIMAL_SIZE_RANGE[1]:\n", "        return \"Excellent\", \"High\", \"Optimal for thesis timeline\"\n", "    elif size_gb <= THESIS_SIZE_LIMIT_GB:\n", "        return \"Good\", \"Medium\", \"Usable with optimization\"\n", "    else:\n", "        return \"Too Large\", \"Low\", \"Skip - Too large for thesis timeline\"\n", "\n", "# Apply assessment\n", "suitability_results = [assess_suitability(size, acc) for size, acc in \n", "                      zip(assessment_df['Actual_Size_GB'], assessment_df['Accessible'])]\n", "\n", "assessment_df['Thesis_Suitability'] = [r[0] for r in suitability_results]\n", "assessment_df['Priority'] = [r[1] for r in suitability_results]\n", "assessment_df['Selection_Remarks'] = [r[2] for r in suitability_results]\n", "\n", "print(\"Thesis Suitability Assessment:\")\n", "print(assessment_df[['Project', 'Actual_Size_GB', 'Accessible', 'Thesis_Suitability', 'Selection_Remarks']])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. <PERSON><PERSON> Recommendations\n", "\n", "Generate optimal dataset combination for 12-week timeline:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter usable datasets\n", "usable = assessment_df[\n", "    (assessment_df['Accessible'] == True) & \n", "    (assessment_df['Actual_Size_GB'] > 0)\n", "].sort_values('Actual_Size_GB')\n", "\n", "print(\"THESIS RECOMMENDATIONS:\")\n", "print(\"=\" * 40)\n", "\n", "if len(usable) >= 2:\n", "    print(\"Recommended combination:\")\n", "    for i, (_, row) in enumerate(usable.head(3).iterrows(), 1):\n", "        print(f\"{i}. {row['Project']}: {row['Actual_Size_GB']} GB - {row['Selection_Remarks']}\")\n", "    \n", "    total_size = usable.head(3)['Actual_Size_GB'].sum()\n", "    print(f\"\\nTotal size: {total_size:.1f} GB\")\n", "    print(f\"Download time: ~{total_size * 2:.0f} minutes\")\n", "    \n", "    print(\"\\nBenefits:\")\n", "    print(\"- Multi-site validation\")\n", "    print(\"- Manageable for 12-week timeline\")\n", "    print(\"- Statistical significance\")\n", "    \n", "else:\n", "    print(\"Limited datasets available - consider chunking larger ones\")\n", "\n", "# Export assessment\n", "assessment_df.to_csv('thesis_dataset_assessment.csv', index=False)\n", "print(f\"\\nAssessment exported to: thesis_dataset_assessment.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> and Next Steps\n", "\n", "**Key Findings:**\n", "- Systematic evaluation identified optimal datasets for thesis timeline\n", "- Multi-site comparison ensures statistical validity\n", "- Total data size manageable for 12-week completion\n", "\n", "**Recommended Action:**\n", "1. Download selected datasets using the data acquisition notebook\n", "2. Begin method implementation on primary dataset\n", "3. Expand to secondary datasets for cross-validation\n", "\n", "**Academic Value:**\n", "- Rigorous dataset selection methodology\n", "- Evidence-based decision making\n", "- Reproducible analysis framework"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}