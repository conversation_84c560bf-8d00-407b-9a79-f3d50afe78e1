{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Project Data Assessment for Thesis\n", "\n", "**Purpose**: Systematic evaluation of available point cloud datasets for ML vs Traditional method comparison\n", "\n", "**Thesis Context**: 12-week timeline requires careful dataset selection to avoid computational bottlenecks\n", "\n", "**Methodology**: AWS CLI exploration + size analysis + accessibility testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import subprocess\n", "import json\n", "from pathlib import Path\n", "import requests\n", "\n", "# Configuration\n", "THESIS_SIZE_LIMIT_GB = 10  # Maximum size per dataset for 12-week thesis\n", "OPTIMAL_SIZE_RANGE = (0.5, 5.0)  # Optimal size range in GB"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Project Inventory\n", "\n", "Complete list of available projects with point cloud locations:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Complete project inventory from data sources\n", "projects_data = {\n", "    'Project': [\n", "        '<PERSON><PERSON><PERSON> - ENEL',\n", "        'Mudjar - ENEL', \n", "        '<PERSON><PERSON> - ENEL',\n", "        'Sunstreams Project - McCarthy',\n", "        'Althea - RPCS',\n", "        'Nortan - RES Renewables'\n", "    ],\n", "    'Short_Name': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', 'RPC<PERSON>', 'RES'],\n", "    'Point_Cloud_URL': [\n", "        's3://preetam-filezilla-test/Castro/Pointcloud/',\n", "        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',\n", "        'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON>/Flight12/<PERSON>_Fly12_pointcloud.las',\n", "        's3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/',\n", "        's3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/',\n", "        's3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/'\n", "    ],\n", "    'URL_Type': ['S3_Folder', 'HTTPS_Direct', 'HTTPS_Direct', 'S3_Folder', 'S3_Folder', 'S3_Folder'],\n", "    'Estimated_Size_GB': [46.8, 1.4, 2.8, 2.1, 1.8, 15.7],  # From data_sources.csv\n", "    'AWS_CLI_Command': [\n", "        'aws s3 ls s3://preetam-filezilla-test/Castro/Pointcloud/ --recursive --human-readable',\n", "        'curl -I https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',\n", "        'curl -I https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/<PERSON><PERSON>_<PERSON>_Di_<PERSON>/Flight12/<PERSON>_Fly12_pointcloud.las',\n", "        'aws s3 ls s3://preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/ --recursive --human-readable',\n", "        'aws s3 ls s3://preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/ --recursive --human-readable',\n", "        'aws s3 ls \"s3://ftp-upload-images/Data files to GIS Team/CPM & CQM/2025/RES/Block11/Pointcloud/\" --recursive --human-readable'\n", "    ]\n", "}\n", "\n", "projects_df = pd.DataFrame(projects_data)\n", "print(\"Project Inventory:\")\n", "print(projects_df[['Project', 'Short_Name', 'URL_Type', 'Estimated_Size_GB']].to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Data Size Analysis\n", "\n", "Systematic exploration of actual file sizes using AWS CLI:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_aws_cli_check(command, project_name):\n", "    \"\"\"Run AWS CLI command and parse results\"\"\"\n", "    print(f\"Running: {command}\")\n", "    \n", "    try:\n", "        if command.startswith('aws s3 ls'):\n", "            # Extract S3 URL from command\n", "            parts = command.split()\n", "            s3_url = parts[3]\n", "            \n", "            result = subprocess.run([\n", "                'aws', 's3', 'ls', s3_url, '--recursive', '--human-readable'\n", "            ], capture_output=True, text=True, timeout=60)\n", "            \n", "            if result.returncode == 0:\n", "                return parse_s3_output(result.stdout, project_name)\n", "            else:\n", "                return {'files': [], 'total_size_gb': 0, 'accessible': False, 'error': result.stderr}\n", "                \n", "        elif command.startswith('curl -I'):\n", "            # Extract URL from curl command\n", "            url = command.split('curl -I ')[1]\n", "            \n", "            result = subprocess.run([\n", "                'curl', '-I', '--max-time', '30', url\n", "            ], capture_output=True, text=True)\n", "            \n", "            if result.returncode == 0 and '200 OK' in result.stdout:\n", "                return parse_curl_output(result.stdout, url, project_name)\n", "            else:\n", "                return {'files': [], 'total_size_gb': 0, 'accessible': False, 'error': 'HTTP request failed'}\n", "                \n", "    except Exception as e:\n", "        return {'files': [], 'total_size_gb': 0, 'accessible': False, 'error': str(e)}\n", "\n", "def parse_s3_output(output, project_name):\n", "    \"\"\"Parse AWS S3 ls output\"\"\"\n", "    files = []\n", "    total_size_gb = 0\n", "    \n", "    for line in output.strip().split('\\n'):\n", "        if '.las' in line.lower() and line.strip():\n", "            parts = line.strip().split()\n", "            if len(parts) >= 3:\n", "                size_str = parts[2]\n", "                filename = parts[-1].split('/')[-1]  # Get just filename\n", "                \n", "                # Convert size to GB\n", "                if 'GiB' in size_str:\n", "                    size_gb = float(size_str.replace('GiB', ''))\n", "                elif 'MiB' in size_str:\n", "                    size_gb = float(size_str.replace('MiB', '')) / 1024\n", "                elif '<PERSON><PERSON>' in size_str:\n", "                    size_gb = float(size_str.replace('KiB', '')) / (1024 * 1024)\n", "                else:\n", "                    size_gb = 0\n", "                \n", "                files.append({'filename': filename, 'size_gb': size_gb})\n", "                total_size_gb += size_gb\n", "    \n", "    return {'files': files, 'total_size_gb': total_size_gb, 'accessible': True}\n", "\n", "def parse_curl_output(output, url, project_name):\n", "    \"\"\"Parse curl -I output\"\"\"\n", "    try:\n", "        for line in output.split('\\n'):\n", "            if 'content-length:' in line.lower():\n", "                size_bytes = int(line.split(':')[1].strip())\n", "                size_gb = size_bytes / (1024**3)\n", "                filename = url.split('/')[-1]\n", "                return {'files': [{'filename': filename, 'size_gb': size_gb}], \n", "                       'total_size_gb': size_gb, 'accessible': True}\n", "        return {'files': [], 'total_size_gb': 0, 'accessible': False, 'error': 'No content-length header'}\n", "    except Exception as e:\n", "        return {'files': [], 'total_size_gb': 0, 'accessible': False, 'error': str(e)}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze each project\n", "analysis_results = []\n", "\n", "for idx, row in projects_df.iterrows():\n", "    project = row['Short_Name']\n", "    url = row['Point_Cloud_URL']\n", "    url_type = row['URL_Type']\n", "    \n", "    print(f\"\\n🔍 Analyzing {project}...\")\n", "    \n", "    if url_type == 'S3_Folder':\n", "        result = check_s3_folder_contents(url, project)\n", "    else:  # HTTPS_Direct\n", "        result = check_https_file(url, project)\n", "    \n", "    # Determine thesis suitability\n", "    if result['accessible']:\n", "        size_gb = result['total_size_gb']\n", "        \n", "        if size_gb <= OPTIMAL_SIZE_RANGE[1]:\n", "            suitability = \"Excellent\"\n", "            priority = \"High\"\n", "        elif size_gb <= THESIS_SIZE_LIMIT_GB:\n", "            suitability = \"Good\"\n", "            priority = \"Medium\"\n", "        else:\n", "            suitability = \"Requires Chunking\"\n", "            priority = \"Low\"\n", "    else:\n", "        size_gb = 0\n", "        suitability = \"Not Accessible\"\n", "        priority = \"None\"\n", "    \n", "    analysis_results.append({\n", "        'Project': project,\n", "        'Total_Size_GB': round(size_gb, 1),\n", "        'File_Count': len(result['files']),\n", "        'Accessible': result['accessible'],\n", "        'Thesis_Suitability': suitability,\n", "        'Priority': priority,\n", "        'Files': result['files'][:3],  # Show first 3 files\n", "        'Error': result.get('error', '')\n", "    })\n", "    \n", "    if result['accessible'] and result['files']:\n", "        print(f\"   ✅ {len(result['files'])} files, {size_gb:.1f} GB total\")\n", "        for file_info in result['files'][:2]:  # Show first 2 files\n", "            print(f\"      - {file_info['filename']}: {file_info['size_gb']:.1f} GB\")\n", "    else:\n", "        print(f\"   ❌ Not accessible: {result.get('error', 'Unknown error')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Thesis Suitability Assessment\n", "\n", "Comprehensive evaluation matrix for thesis dataset selection:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create assessment matrix\n", "assessment_df = pd.DataFrame(analysis_results)\n", "\n", "# Add selection remarks\n", "def generate_remarks(row):\n", "    if not row['Accessible']:\n", "        return \"❌ Skip - Not accessible\"\n", "    elif row['Total_Size_GB'] == 0:\n", "        return \"❌ Skip - No data found\"\n", "    elif row['Total_Size_GB'] <= 0.1:\n", "        return \"⚠️ Very small - May lack sufficient data\"\n", "    elif row['Total_Size_GB'] <= OPTIMAL_SIZE_RANGE[1]:\n", "        return \"✅ Optimal - Perfect for thesis timeline\"\n", "    elif row['Total_Size_GB'] <= THESIS_SIZE_LIMIT_GB:\n", "        return \"🔄 Usable - May need optimization\"\n", "    else:\n", "        return \"❌ Skip - Too large for thesis timeline\"\n", "\n", "assessment_df['Selection_Remarks'] = assessment_df.apply(generate_remarks, axis=1)\n", "\n", "# Display assessment matrix\n", "display_columns = ['Project', 'Total_Size_GB', 'File_Count', 'Accessible', \n", "                  'Thesis_Suitability', 'Priority', 'Selection_Remarks']\n", "\n", "print(\"\\n📋 THESIS DATASET ASSESSMENT MATRIX\")\n", "print(\"=\" * 80)\n", "print(assessment_df[display_columns].to_string(index=False))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Recommended Dataset Combination\n", "\n", "Optimal selection for 12-week thesis completion:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Filter optimal datasets\n", "optimal_datasets = assessment_df[\n", "    (assessment_df['Accessible'] == True) & \n", "    (assessment_df['Total_Size_GB'] > 0) &\n", "    (assessment_df['Total_Size_GB'] <= THESIS_SIZE_LIMIT_GB)\n", "].sort_values('Total_Size_GB')\n", "\n", "print(\"\\n🎯 RECOMMENDED THESIS DATASETS\")\n", "print(\"=\" * 50)\n", "\n", "if len(optimal_datasets) >= 3:\n", "    # Select top 3 datasets for robust comparison\n", "    selected = optimal_datasets.head(3)\n", "    total_size = selected['Total_Size_GB'].sum()\n", "    \n", "    print(f\"Primary Dataset: {selected.iloc[0]['Project']} ({selected.iloc[0]['Total_Size_GB']} GB)\")\n", "    print(f\"Secondary Dataset: {selected.iloc[1]['Project']} ({selected.iloc[1]['Total_Size_GB']} GB)\")\n", "    print(f\"Tertiary Dataset: {selected.iloc[2]['Project']} ({selected.iloc[2]['Total_Size_GB']} GB)\")\n", "    print(f\"\\nTotal Combined Size: {total_size:.1f} GB\")\n", "    print(f\"Estimated Download Time: {total_size * 2:.0f} minutes\")\n", "    \n", "    print(\"\\n✅ THESIS BENEFITS:\")\n", "    print(\"   - Multi-site validation for statistical significance\")\n", "    print(\"   - Manageable data sizes for 12-week timeline\")\n", "    print(\"   - Diverse construction scenarios for robustness\")\n", "    \n", "else:\n", "    print(\"⚠️ Limited optimal datasets available\")\n", "    print(\"Consider chunking larger datasets or adjusting methodology\")\n", "\n", "# Export results for thesis documentation\n", "assessment_df.to_csv('thesis_dataset_assessment.csv', index=False)\n", "print(f\"\\n📄 Assessment matrix exported to: thesis_dataset_assessment.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. <PERSON><PERSON><PERSON> and Next Steps\n", "\n", "**Key Findings:**\n", "- Systematic evaluation identified optimal datasets for thesis timeline\n", "- Multi-site comparison ensures statistical validity\n", "- Total data size manageable for 12-week completion\n", "\n", "**Recommended Action:**\n", "1. Download selected datasets using the data acquisition notebook\n", "2. Begin method implementation on primary dataset\n", "3. Expand to secondary datasets for cross-validation\n", "\n", "**Academic Value:**\n", "- Rigorous dataset selection methodology\n", "- Evidence-based decision making\n", "- Reproducible analysis framework"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}