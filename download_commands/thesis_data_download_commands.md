# Thesis Data Download Commands

## Overview

Based on the systematic data assessment, download the optimal dataset combination for thesis work:
- **RPCS**: Primary dataset (1.3 GB) - Excellent multi-modal data
- **McCarthy**: Secondary dataset (36 MB) - Perfect for testing
- **Castro**: Selective dataset (7 GB) - Use area4_point.las only

**Total Size**: ~8.3 GB  
**Estimated Time**: ~17 minutes

## Prerequisites

Ensure rclone is configured with the correct remote:
```bash
rclone config show datasee-s3
```

## Directory Setup

```bash
# Create thesis data directory structure
mkdir -p data/thesis_datasets/{pointcloud,cad,orthomosaic}
cd data/thesis_datasets
```

## Primary Dataset: RPCS (Recommended)

### Point Cloud Data
```bash
# RPCS Point Cloud (1.3 GB) - Primary dataset
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las pointcloud/ --progress

# RPCS Point Cloud alternative (113 MB) - Smaller version if needed
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud_0.5.las pointcloud/ --progress
```

### CAD Data
```bash
# RPCS CAD Files
rclone copy datasee-s3:ftp-rpcs/Althea/CAD\ Files/ cad/rpcs/ --progress
```

### Orthomosaic Data
```bash
# RPCS Orthomosaic
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Ortho/ orthomosaic/rpcs/ --progress
```

## Secondary Dataset: McCarthy (Testing)

### Point Cloud Data
```bash
# McCarthy Point Cloud (36 MB) - Tiny but complete
rclone copy "datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las" pointcloud/ --progress
```

### CAD Data
```bash
# McCarthy CAD Files
rclone copy datasee-s3:ftp-mccarthy/CAD\ Files/ cad/mccarthy/ --progress
```

### Orthomosaic Data
```bash
# McCarthy RGB/Orthomosaic
rclone copy datasee-s3:preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ orthomosaic/mccarthy/ --progress
```

## Selective Dataset: Castro (Area4 Only)

### Point Cloud Data
```bash
# Castro Area4 Point Cloud ONLY (7 GB) - Most manageable Castro file
rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las pointcloud/ --progress

# DO NOT download these large files (unless specifically needed):
# rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/Area1_part1_point.las pointcloud/ --progress  # 15.2 GB
# rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/Area2_point.las pointcloud/ --progress        # 10.7 GB
# rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/Area1_Part2_Points.las pointcloud/ --progress # 13.9 GB
```

### CAD Data
```bash
# Castro CAD Files (2025 data)
rclone copy datasee-s3:ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ cad/castro/ --progress
```

### Orthomosaic Data
```bash
# Castro Orthomosaic
rclone copy datasee-s3:preetam-filezilla-test/Castro/Ortho/ orthomosaic/castro/ --progress
```

## Optional: Additional CAD Reference Data

### Mudjar CAD (DWG file only)
```bash
# Mudjar essential CAD file (36.4 MB DWG + KML files)
rclone copy datasee-s3:ftp-enel/mudejar-spain/2024-10-22/CAD/gre.eec.d.21.es.p.11730.00.149.04.dwg cad/mudjar/ --progress
rclone copy datasee-s3:ftp-enel/mudejar-spain/2024-09-23/KML/psfv_-_mudéjar_1.kmz cad/mudjar/ --progress
rclone copy datasee-s3:ftp-enel/mudejar-spain/2024-10-23/KML/mudejar_ejemplo.kml cad/mudjar/ --progress
```

## Complete Download Script

Save as `download_thesis_data.sh`:

```bash
#!/bin/bash

# Thesis Data Download Script
echo "Starting thesis dataset download..."
echo "Total estimated size: 8.3 GB"
echo "Estimated time: 17 minutes"

# Create directory structure
mkdir -p data/thesis_datasets/{pointcloud,cad,orthomosaic}
cd data/thesis_datasets

echo "Downloading RPCS (Primary Dataset)..."
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las pointcloud/ --progress
rclone copy datasee-s3:ftp-rpcs/Althea/CAD\ Files/ cad/rpcs/ --progress
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Ortho/ orthomosaic/rpcs/ --progress

echo "Downloading McCarthy (Secondary Dataset)..."
rclone copy "datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las" pointcloud/ --progress
rclone copy datasee-s3:ftp-mccarthy/CAD\ Files/ cad/mccarthy/ --progress
rclone copy datasee-s3:preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ orthomosaic/mccarthy/ --progress

echo "Downloading Castro Area4 (Selective Dataset)..."
rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las pointcloud/ --progress
rclone copy datasee-s3:ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ cad/castro/ --progress
rclone copy datasee-s3:preetam-filezilla-test/Castro/Ortho/ orthomosaic/castro/ --progress

echo "Download complete!"
echo "Verify downloads:"
ls -lh pointcloud/
ls -lh cad/
ls -lh orthomosaic/
```

## Verification Commands

After download, verify the files:

```bash
# Check point cloud files
ls -lh pointcloud/
du -sh pointcloud/

# Check CAD files
find cad/ -name "*.dwg" -o -name "*.dxf" -o -name "*.ifc" | head -10

# Check orthomosaic files
find orthomosaic/ -name "*.tif" -o -name "*.tiff" -o -name "*.jpg" | head -10

# Total size check
du -sh data/thesis_datasets/
```

## Troubleshooting

If downloads fail:

1. **Check rclone config**: `rclone config show datasee-s3`
2. **Test connection**: `rclone ls datasee-s3:preetam-filezilla-test/ | head -5`
3. **Retry specific files**: Add `--retries 3` flag
4. **Check disk space**: `df -h`

## Pre-Download Setup

### Local Environment Preparation
```bash
# Check available disk space (need ~10 GB free)
df -h

# Check rclone version and config
rclone version
rclone config show datasee-s3

# Test connection
rclone ls datasee-s3:preetam-filezilla-test/ | head -5

# Create backup of current data (if any)
cp -r data/thesis_datasets data/thesis_datasets_backup_$(date +%Y%m%d) 2>/dev/null || true
```

### Download Validation
```bash
# After each major download, verify integrity
rclone check datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las pointcloud/ --one-way

# Check file sizes match expectations
ls -lh pointcloud/*.las
```

## Google Drive Backup Commands

### Setup Google Drive Remote (if not configured)
```bash
# Configure Google Drive remote (run once)
rclone config create gdrive drive
```

### Upload to Google Drive
```bash
# Upload entire thesis dataset to Google Drive
rclone copy data/thesis_datasets/ gdrive:thesis_datasets/ --progress --transfers 4

# Upload with folder structure preserved
rclone sync data/thesis_datasets/ gdrive:thesis_datasets/ --progress --dry-run  # Test first
rclone sync data/thesis_datasets/ gdrive:thesis_datasets/ --progress           # Actual sync

# Upload specific datasets separately (recommended for large files)
rclone copy data/thesis_datasets/pointcloud/ gdrive:thesis_datasets/pointcloud/ --progress
rclone copy data/thesis_datasets/cad/ gdrive:thesis_datasets/cad/ --progress
rclone copy data/thesis_datasets/orthomosaic/ gdrive:thesis_datasets/orthomosaic/ --progress
```

### Incremental Backup Strategy
```bash
# Create dated backup folder
DATE=$(date +%Y%m%d)
rclone copy data/thesis_datasets/ gdrive:thesis_backups/backup_$DATE/ --progress

# Sync only changes (faster for updates)
rclone sync data/thesis_datasets/ gdrive:thesis_datasets/ --progress --update
```

## Additional Recommended Downloads

### Project Documentation
```bash
# Download any README or documentation files
rclone copy datasee-s3:preetam-filezilla-test/Castro/ cad/castro/docs/ --include "*.txt" --include "*.pdf" --include "*.md" --progress
rclone copy datasee-s3:preetam-filezilla-test/RCPS/ cad/rpcs/docs/ --include "*.txt" --include "*.pdf" --include "*.md" --progress
```

### Sample RGB Images (for method validation)
```bash
# Download small sample of RGB images for validation (limit to avoid huge downloads)
rclone copy datasee-s3:preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ orthomosaic/mccarthy/samples/ --include "*.jpg" --max-size 10M --max-files 50 --progress
```

### Metadata Files
```bash
# Download any coordinate system or metadata files
rclone copy datasee-s3:preetam-filezilla-test/Castro/ cad/castro/metadata/ --include "*.prj" --include "*.xml" --include "*.json" --progress
```

## Complete Download + Backup Script

Save as `download_and_backup.sh`:
```bash
#!/bin/bash

echo "=== THESIS DATA DOWNLOAD AND BACKUP ==="
echo "Total estimated size: 8.3 GB"
echo "Estimated time: 17 minutes download + 10 minutes backup"

# Pre-flight checks
echo "Checking prerequisites..."
df -h | grep -E "(Avail|Available)"
rclone version | head -1

# Create directory structure
mkdir -p data/thesis_datasets/{pointcloud,cad,orthomosaic,metadata}
cd data/thesis_datasets

# Download datasets (your existing commands)
echo "Downloading RPCS (Primary Dataset)..."
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las pointcloud/ --progress
rclone copy datasee-s3:ftp-rpcs/Althea/CAD\ Files/ cad/rpcs/ --progress
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Ortho/ orthomosaic/rpcs/ --progress

echo "Downloading McCarthy (Secondary Dataset)..."
rclone copy "datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las" pointcloud/ --progress
rclone copy datasee-s3:ftp-mccarthy/CAD\ Files/ cad/mccarthy/ --progress
rclone copy datasee-s3:preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ orthomosaic/mccarthy/ --progress

echo "Downloading Castro Area4 (Selective Dataset)..."
rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las pointcloud/ --progress
rclone copy datasee-s3:ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ cad/castro/ --progress
rclone copy datasee-s3:preetam-filezilla-test/Castro/Ortho/ orthomosaic/castro/ --progress

# Verification
echo "Verifying downloads..."
ls -lh pointcloud/
du -sh .

# Backup to Google Drive
echo "Backing up to Google Drive..."
DATE=$(date +%Y%m%d_%H%M)
rclone copy . gdrive:thesis_datasets_$DATE/ --progress

echo "=== DOWNLOAD AND BACKUP COMPLETE ==="
echo "Local data: $(pwd)"
echo "Google Drive backup: gdrive:thesis_datasets_$DATE/"
```

## Notes

- **Excluded datasets**: Mudjar and Giorgio (point cloud access issues)
- **Excluded large files**: Castro Area1, Area2 files (too large for thesis timeline)
- **RES dataset**: Excluded due to 15.7 GB size
- **Priority order**: Download RPCS first (most critical), then McCarthy, then Castro
- **Backup strategy**: If any download fails, the other datasets provide sufficient data for thesis completion
- **Google Drive**: Provides cloud backup and sharing capability for thesis work

## File Organization

Final structure:
```
data/thesis_datasets/
├── pointcloud/
│   ├── Point_Cloud.las (RPCS - 1.3 GB)
│   ├── Buffer_las(rev1).las (McCarthy - 36 MB)
│   └── area4_point.las (Castro - 7 GB)
├── cad/
│   ├── rpcs/
│   ├── mccarthy/
│   └── castro/
└── orthomosaic/
    ├── rpcs/
    ├── mccarthy/
    └── castro/
```

This provides a complete multi-modal dataset for thesis work with manageable size and reliable access.
