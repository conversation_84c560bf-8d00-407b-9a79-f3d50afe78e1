# Thesis Data Download Commands

## Overview

Based on the systematic data assessment, download the optimal dataset combination for thesis work:
- **RPCS**: Primary dataset (1.3 GB) - Excellent multi-modal data
- **McCarthy**: Secondary dataset (36 MB) - Perfect for testing
- **Castro**: Selective dataset (7 GB) - Use area4_point.las only

**Total Size**: ~8.3 GB  
**Estimated Time**: ~17 minutes

## Prerequisites

Ensure rclone is configured with the correct remote:
```bash
rclone config show datasee-s3
```

## Directory Setup

```bash
# Create thesis data directory structure
mkdir -p data/thesis_datasets/{pointcloud,cad,orthomosaic}
cd data/thesis_datasets
```

## Primary Dataset: RPCS (Recommended)

### Point Cloud Data
```bash
# RPCS Point Cloud (1.3 GB) - Primary dataset
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las pointcloud/ --progress

# RPCS Point Cloud alternative (113 MB) - Smaller version if needed
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud_0.5.las pointcloud/ --progress
```

### CAD Data
```bash
# RPCS CAD Files
rclone copy datasee-s3:ftp-rpcs/Althea/CAD\ Files/ cad/rpcs/ --progress
```

### Orthomosaic Data
```bash
# RPCS Orthomosaic
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Ortho/ orthomosaic/rpcs/ --progress
```

## Secondary Dataset: McCarthy (Testing)

### Point Cloud Data
```bash
# McCarthy Point Cloud (36 MB) - Tiny but complete
rclone copy "datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las" pointcloud/ --progress
```

### CAD Data
```bash
# McCarthy CAD Files
rclone copy datasee-s3:ftp-mccarthy/CAD\ Files/ cad/mccarthy/ --progress
```

### Orthomosaic Data
```bash
# McCarthy RGB/Orthomosaic
rclone copy datasee-s3:preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ orthomosaic/mccarthy/ --progress
```

## Selective Dataset: Castro (Area4 Only)

### Point Cloud Data
```bash
# Castro Area4 Point Cloud ONLY (7 GB) - Most manageable Castro file
rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las pointcloud/ --progress

# DO NOT download these large files (unless specifically needed):
# rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/Area1_part1_point.las pointcloud/ --progress  # 15.2 GB
# rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/Area2_point.las pointcloud/ --progress        # 10.7 GB
# rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/Area1_Part2_Points.las pointcloud/ --progress # 13.9 GB
```

### CAD Data
```bash
# Castro CAD Files (2025 data)
rclone copy datasee-s3:ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ cad/castro/ --progress
```

### Orthomosaic Data
```bash
# Castro Orthomosaic
rclone copy datasee-s3:preetam-filezilla-test/Castro/Ortho/ orthomosaic/castro/ --progress
```

## Optional: Additional CAD Reference Data

### Mudjar CAD (DWG file only)
```bash
# Mudjar essential CAD file (36.4 MB DWG + KML files)
rclone copy datasee-s3:ftp-enel/mudejar-spain/2024-10-22/CAD/gre.eec.d.21.es.p.11730.00.149.04.dwg cad/mudjar/ --progress
rclone copy datasee-s3:ftp-enel/mudejar-spain/2024-09-23/KML/psfv_-_mudéjar_1.kmz cad/mudjar/ --progress
rclone copy datasee-s3:ftp-enel/mudejar-spain/2024-10-23/KML/mudejar_ejemplo.kml cad/mudjar/ --progress
```

## Complete Download Script

Save as `download_thesis_data.sh`:

```bash
#!/bin/bash

# Thesis Data Download Script
echo "Starting thesis dataset download..."
echo "Total estimated size: 8.3 GB"
echo "Estimated time: 17 minutes"

# Create directory structure
mkdir -p data/thesis_datasets/{pointcloud,cad,orthomosaic}
cd data/thesis_datasets

echo "Downloading RPCS (Primary Dataset)..."
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Point_Cloud/Point_Cloud.las pointcloud/ --progress
rclone copy datasee-s3:ftp-rpcs/Althea/CAD\ Files/ cad/rpcs/ --progress
rclone copy datasee-s3:preetam-filezilla-test/RCPS/Updated_031024/Ortho/ orthomosaic/rpcs/ --progress

echo "Downloading McCarthy (Secondary Dataset)..."
rclone copy "datasee-s3:preetam-filezilla-test/McCarthy_Fly2/Point_Cloud/Buffer_las(rev1).las" pointcloud/ --progress
rclone copy datasee-s3:ftp-mccarthy/CAD\ Files/ cad/mccarthy/ --progress
rclone copy datasee-s3:preetam-filezilla-test/McCarthy_Fly2/RGB_Ortho/ orthomosaic/mccarthy/ --progress

echo "Downloading Castro Area4 (Selective Dataset)..."
rclone copy datasee-s3:preetam-filezilla-test/Castro/Pointcloud/area4_point.las pointcloud/ --progress
rclone copy datasee-s3:ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ cad/castro/ --progress
rclone copy datasee-s3:preetam-filezilla-test/Castro/Ortho/ orthomosaic/castro/ --progress

echo "Download complete!"
echo "Verify downloads:"
ls -lh pointcloud/
ls -lh cad/
ls -lh orthomosaic/
```

## Verification Commands

After download, verify the files:

```bash
# Check point cloud files
ls -lh pointcloud/
du -sh pointcloud/

# Check CAD files
find cad/ -name "*.dwg" -o -name "*.dxf" -o -name "*.ifc" | head -10

# Check orthomosaic files
find orthomosaic/ -name "*.tif" -o -name "*.tiff" -o -name "*.jpg" | head -10

# Total size check
du -sh data/thesis_datasets/
```

## Troubleshooting

If downloads fail:

1. **Check rclone config**: `rclone config show datasee-s3`
2. **Test connection**: `rclone ls datasee-s3:preetam-filezilla-test/ | head -5`
3. **Retry specific files**: Add `--retries 3` flag
4. **Check disk space**: `df -h`

## Notes

- **Excluded datasets**: Mudjar and Giorgio (point cloud access issues)
- **Excluded large files**: Castro Area1, Area2 files (too large for thesis timeline)
- **RES dataset**: Excluded due to 15.7 GB size
- **Priority order**: Download RPCS first (most critical), then McCarthy, then Castro
- **Backup strategy**: If any download fails, the other datasets provide sufficient data for thesis completion

## File Organization

Final structure:
```
data/thesis_datasets/
├── pointcloud/
│   ├── Point_Cloud.las (RPCS - 1.3 GB)
│   ├── Buffer_las(rev1).las (McCarthy - 36 MB)
│   └── area4_point.las (Castro - 7 GB)
├── cad/
│   ├── rpcs/
│   ├── mccarthy/
│   └── castro/
└── orthomosaic/
    ├── rpcs/
    ├── mccarthy/
    └── castro/
```

This provides a complete multi-modal dataset for thesis work with manageable size and reliable access.
