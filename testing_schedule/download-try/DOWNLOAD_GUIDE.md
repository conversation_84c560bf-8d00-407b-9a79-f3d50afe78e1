# 📥 Data Download Guide

## 🚀 **Quick Start**

### **1. Initial Setup**
```bash
# Make scripts executable
chmod +x setup_download_environment.sh
chmod +x download_project_data.sh

# Run setup (interactive menu)
./setup_download_environment.sh

# Or run full automated setup
./setup_download_environment.sh full
```

### **2. Download All Data**
```bash
# Download all projects
./download_project_data.sh

# Or test first with dry run
DRY_RUN=true ./download_project_data.sh
```

### **3. Download Specific Projects**
```bash
# ENEL projects
./download_project_data.sh castro
./download_project_data.sh mudjar
./download_project_data.sh giorgio

# USA projects
./download_project_data.sh mccarthy
./download_project_data.sh rpcs
./download_project_data.sh res

# By region
./download_project_data.sh enel    # All ENEL projects
./download_project_data.sh usa     # All USA projects
```

---

## 📊 **Project Data Overview**

| Project | Type | Point Cloud | CAD | Ortho | Est. Size |
|---------|------|-------------|-----|-------|-----------|
| **Castro** | ENEL | S3 | S3 | S3 | ~3.6 GB |
| **Mudjar** | ENEL | HTTPS | S3 | - | ~3.4 GB |
| **Giorgio** | ENEL | HTTPS | S3 | HTTPS | ~4.15 GB |
| **McCarthy** | USA | S3 | S3 | S3 | ~6.4 GB |
| **RPCS** | USA | S3 | S3 | S3 | ~5.55 GB |
| **RES** | USA | S3 | S3 | HTTPS | ~8.1 GB |

**Total Estimated Size**: ~31.2 GB

---

## 🔧 **Configuration**

### **Environment Variables (.env)**
```bash
# Core settings
GDRIVE_REMOTE="gdrive"                    # Google Drive remote name
GDRIVE_BASE_DIR="energy-inspection-3d-data"  # Base directory in Google Drive
LOCAL_DATA_DIR="./data"                   # Local data directory
SKIP_EXISTING="true"                      # Skip existing files
DRY_RUN="false"                          # Test mode (don't actually download)
VERBOSE="false"                          # Verbose output

# Performance settings
PARALLEL_DOWNLOADS=4                      # Concurrent downloads
RCLONE_TRANSFERS=4                       # Rclone transfer threads
RCLONE_CHECKERS=8                        # Rclone checker threads

# Backup settings
BACKUP_TO_GDRIVE="true"                  # Backup to Google Drive after download
KEEP_LOCAL_COPIES="true"                 # Keep local files after backup
```

### **Required Credentials**
1. **Google Drive**: Configured via `rclone config`
2. **AWS S3**: Configured via `aws configure` or environment variables
3. **Network Access**: For HTTPS downloads

---

## 📁 **Directory Structure**

### **Input Data Organization**
```
data/
├── ENEL/
│   ├── Castro/
│   │   ├── pointcloud/     # Point cloud files (.las, .laz)
│   │   ├── cad/           # CAD files (.dwg, .dxf)
│   │   └── ortho/         # Orthomosaic images (.tif)
│   ├── Mudjar/
│   └── Giorgio/
└── USA/
    ├── McCarthy/
    ├── RPCS/
    └── RES/
```

### **Google Drive Backup Structure**
```
energy-inspection-3d-data/
├── ENEL/
│   ├── Castro/
│   ├── Mudjar/
│   └── Giorgio/
└── USA/
    ├── McCarthy/
    ├── RPCS/
    └── RES/
```

---

## 🎯 **Download Sources**

### **S3 Buckets**
- `s3://preetam-filezilla-test/` - Main test bucket
- `s3://ftp-enel/` - ENEL project files
- `s3://ftp-mccarthy/` - McCarthy project files
- `s3://ftp-rpcs/` - RPCS project files
- `s3://ftp-upload-images/` - Additional project files

### **HTTPS URLs**
- Point cloud files from `ftp-upload-images.s3.ap-south-1.amazonaws.com`
- Orthomosaic files from various HTTPS sources

---

## 🔍 **Monitoring and Logs**

### **Log Files**
```bash
# Main download log
tail -f logs/data_download.log

# Error log
tail -f logs/download_errors.log

# Download summary
ls logs/download_summary_*.txt
```

### **Progress Monitoring**
```bash
# Check download progress
watch -n 5 'du -sh data/'

# Monitor active downloads
ps aux | grep -E "(rclone|wget)"

# Check network usage
iftop  # or nethogs
```

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **1. S3 Access Denied**
```bash
# Check AWS credentials
aws configure list
aws sts get-caller-identity

# Test S3 access
aws s3 ls s3://bucket-name/ --region us-east-1
```

#### **2. Google Drive Authentication**
```bash
# Reconfigure Google Drive
rclone config

# Test Google Drive access
rclone lsd gdrive:
```

#### **3. Network/Download Issues**
```bash
# Test connectivity
curl -I "https://test-url"

# Check disk space
df -h

# Resume interrupted downloads
./download_project_data.sh  # Script handles resume automatically
```

#### **4. Permission Issues**
```bash
# Fix script permissions
chmod +x *.sh

# Fix directory permissions
chmod -R 755 data/ logs/
```

### **Performance Optimization**

#### **For High-Speed Connections**
```bash
# Edit .env file
RCLONE_TRANSFERS=8
RCLONE_CHECKERS=16
PARALLEL_DOWNLOADS=8
RCLONE_BUFFER_SIZE="32M"
```

#### **For Limited Bandwidth**
```bash
# Edit .env file
RCLONE_TRANSFERS=2
RCLONE_CHECKERS=4
PARALLEL_DOWNLOADS=2
MAX_BANDWIDTH="10M"
```

#### **For Limited Storage**
```bash
# Download and backup incrementally
BACKUP_TO_GDRIVE="true"
KEEP_LOCAL_COPIES="false"

# Download specific projects only
./download_project_data.sh castro
./download_project_data.sh mudjar
# ... process each project individually
```

---

## 📋 **Validation Commands**

### **Verify Downloads**
```bash
# Check file counts
find data/ -name "*.las" | wc -l
find data/ -name "*.dwg" | wc -l
find data/ -name "*.tif" | wc -l

# Check file sizes
find data/ -type f -exec ls -lh {} \; | sort -k5 -hr

# Verify file integrity (if checksums available)
find data/ -name "*.md5" -exec md5sum -c {} \;
```

### **Google Drive Verification**
```bash
# List backed up files
rclone ls gdrive:energy-inspection-3d-data/

# Compare local vs Google Drive
rclone check data/ gdrive:energy-inspection-3d-data/
```

---

## 🎯 **Usage Examples**

### **Development/Testing**
```bash
# Test download without actually downloading
DRY_RUN=true VERBOSE=true ./download_project_data.sh

# Download only Castro for testing
./download_project_data.sh castro

# Skip existing files and run quietly
SKIP_EXISTING=true VERBOSE=false ./download_project_data.sh
```

### **Production Download**
```bash
# Full download with backup
BACKUP_TO_GDRIVE=true ./download_project_data.sh

# High-performance download
RCLONE_TRANSFERS=8 PARALLEL_DOWNLOADS=8 ./download_project_data.sh
```

### **Incremental Updates**
```bash
# Download only new/changed files
SKIP_EXISTING=true ./download_project_data.sh

# Sync with Google Drive
rclone sync data/ gdrive:energy-inspection-3d-data/
```

---

## 📞 **Support**

### **Before Reporting Issues**
1. Check log files: `logs/data_download.log`
2. Verify credentials: `aws configure list`, `rclone listremotes`
3. Test connectivity: `curl -I "https://test-url"`
4. Check disk space: `df -h`

### **Issue Reporting Template**
```
Environment:
- OS: [Linux/macOS/Windows]
- rclone version: [output of `rclone version`]
- AWS CLI version: [output of `aws --version`]

Issue:
- Project: [Castro/Mudjar/etc.]
- Error message: [exact error text]
- Log excerpt: [relevant log lines]

Steps to reproduce:
1. [step 1]
2. [step 2]
3. [step 3]
```

---

**🎯 Goal**: Successfully download all 31+ GB of project data across 6 projects for pipeline testing and development.
