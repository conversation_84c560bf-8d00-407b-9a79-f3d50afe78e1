# =============================================================================
# DOWNLOAD CONFIGURATION
# =============================================================================
# This file contains all download-specific settings
# Edit these values to customize download behavior

# =============================================================================
# DATA SOURCES CONFIGURATION
# =============================================================================

# CSV file containing all data source URLs and paths
DATA_SOURCES_CSV="data_sources.csv"

# =============================================================================
# S3 DOWNLOAD CONFIGURATION
# =============================================================================

# S3 multipart download settings
S3_MULTIPART_THRESHOLD="64MB"          # Files larger than this use multipart
S3_MULTIPART_CHUNKSIZE="16MB"          # Size of each part in multipart upload
S3_MAX_CONCURRENT_REQUESTS=10          # Maximum concurrent requests
S3_MAX_BANDWIDTH=""                    # Bandwidth limit (e.g., "50MB/s", empty for unlimited)

# S3 retry settings
S3_MAX_ATTEMPTS=3                      # Maximum retry attempts
S3_RETRY_MODE="adaptive"               # Retry mode: standard, adaptive

# =============================================================================
# PROGRESS DISPLAY CONFIGURATION
# =============================================================================

# Progress bar settings
SHOW_PROGRESS_BAR="true"               # Show progress bars for downloads
PROGRESS_UPDATE_INTERVAL=1             # Update interval in seconds
SHOW_TRANSFER_RATE="true"              # Show transfer rate (MB/s)
SHOW_ETA="true"                        # Show estimated time remaining
SHOW_FILE_COUNT="true"                 # Show file count progress

# Console output settings
PROGRESS_BAR_WIDTH=50                  # Width of progress bar in characters
USE_COLORS="true"                      # Use colored output
CLEAR_COMPLETED_PROGRESS="false"       # Clear progress bars when complete

# =============================================================================
# THREADING AND CONCURRENCY
# =============================================================================

# AWS CLI threading
AWS_CLI_MAX_CONCURRENT_REQUESTS=10     # Concurrent requests for AWS CLI
AWS_CLI_MAX_BANDWIDTH=""               # Bandwidth limit for AWS CLI

# rclone threading
RCLONE_TRANSFERS=8                     # Number of file transfers to run in parallel
RCLONE_CHECKERS=16                     # Number of checkers to run in parallel
RCLONE_BUFFER_SIZE="32M"               # Buffer size for each transfer

# wget threading (for HTTPS downloads)
WGET_MAX_CONCURRENT=4                  # Maximum concurrent wget processes
WGET_TIMEOUT=300                       # Timeout in seconds
WGET_RETRIES=3                         # Number of retries

# =============================================================================
# DOWNLOAD OPTIMIZATION
# =============================================================================

# File handling
SKIP_EXISTING_FILES="true"             # Skip files that already exist locally
VERIFY_CHECKSUMS="true"                # Verify file integrity after download
CREATE_CHECKSUMS="true"                # Create checksums for downloaded files

# Compression and encoding
USE_COMPRESSION="false"                # Use compression during transfer
PRESERVE_TIMESTAMPS="true"             # Preserve original file timestamps

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================

# Local storage paths
LOCAL_DATA_DIR="../data"               # Base directory for downloaded data
TEMP_DOWNLOAD_DIR="/tmp/energy-downloads"  # Temporary directory for partial downloads

# Disk space management
MIN_FREE_SPACE_GB=10                   # Minimum free space to maintain (GB)
MAX_TOTAL_SIZE_GB=100                  # Maximum total download size (0 = unlimited)

# =============================================================================
# GOOGLE DRIVE BACKUP CONFIGURATION
# =============================================================================

# Google Drive settings
GDRIVE_REMOTE="gdrive"                 # rclone remote name for Google Drive
GDRIVE_BASE_DIR="energy-inspection-3d-data"  # Base directory in Google Drive
BACKUP_TO_GDRIVE="true"                # Enable Google Drive backup
GDRIVE_PARALLEL_UPLOADS=4              # Concurrent uploads to Google Drive

# Backup behavior
BACKUP_AFTER_EACH_PROJECT="false"     # Backup after each project (vs. at end)
DELETE_LOCAL_AFTER_BACKUP="false"     # Delete local files after successful backup
GDRIVE_VERIFY_UPLOADS="true"          # Verify uploads to Google Drive

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================

# Log files
DOWNLOAD_LOG="logs/download.log"       # Main download log
PROGRESS_LOG="logs/progress.log"       # Progress tracking log
ERROR_LOG="logs/errors.log"            # Error log

# Log levels
LOG_LEVEL="INFO"                       # DEBUG, INFO, WARN, ERROR
LOG_PROGRESS_DETAILS="true"            # Log detailed progress information
LOG_TRANSFER_STATS="true"              # Log transfer statistics

# =============================================================================
# NOTIFICATION SETTINGS
# =============================================================================

# Progress notifications
NOTIFY_ON_PROJECT_COMPLETE="false"     # Notify when each project completes
NOTIFY_ON_ALL_COMPLETE="false"         # Notify when all downloads complete
NOTIFICATION_EMAIL=""                  # Email for notifications
SLACK_WEBHOOK=""                       # Slack webhook URL

# =============================================================================
# ADVANCED SETTINGS
# =============================================================================

# Network settings
CONNECTION_TIMEOUT=30                  # Connection timeout in seconds
READ_TIMEOUT=300                       # Read timeout in seconds
MAX_REDIRECTS=10                       # Maximum HTTP redirects to follow

# Error handling
CONTINUE_ON_ERROR="true"               # Continue downloading other files on error
FAIL_FAST="false"                      # Stop all downloads on first error
MAX_TOTAL_ERRORS=10                    # Maximum total errors before stopping

# Performance tuning
MEMORY_LIMIT_MB=2048                   # Memory limit for download processes
CPU_LIMIT_PERCENT=80                   # CPU usage limit (percentage)

# =============================================================================
# DEBUGGING AND TESTING
# =============================================================================

# Debug settings
DRY_RUN="false"                        # Show what would be downloaded without downloading
VERBOSE="false"                        # Verbose output
DEBUG_MODE="false"                     # Enable debug mode
TRACE_COMMANDS="false"                 # Trace all executed commands

# Testing settings
DOWNLOAD_SAMPLE_ONLY="false"           # Download only first few files for testing
SAMPLE_SIZE_LIMIT_MB=100               # Size limit for sample downloads
TEST_CONNECTIVITY_FIRST="true"        # Test connectivity before starting downloads

# =============================================================================
# EXAMPLE CONFIGURATIONS
# =============================================================================

# High-speed connection (uncomment to use):
# S3_MAX_CONCURRENT_REQUESTS=20
# RCLONE_TRANSFERS=16
# RCLONE_CHECKERS=32
# AWS_CLI_MAX_CONCURRENT_REQUESTS=20

# Limited bandwidth (uncomment to use):
# S3_MAX_BANDWIDTH="10MB/s"
# AWS_CLI_MAX_BANDWIDTH="10MB/s"
# RCLONE_TRANSFERS=2
# RCLONE_CHECKERS=4

# Development/testing (uncomment to use):
# DRY_RUN="true"
# VERBOSE="true"
# DEBUG_MODE="true"
# DOWNLOAD_SAMPLE_ONLY="true"
