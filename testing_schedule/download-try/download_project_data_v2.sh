#!/bin/bash

# =============================================================================
# Energy Inspection 3D - Enhanced Project Data Download Script v2
# =============================================================================
# Features:
# - CSV-based configuration for easy path editing
# - Visible progress bars and transfer rates
# - Multipart/threaded S3 downloads
# - Enhanced error handling and logging
# =============================================================================

set -euo pipefail

# =============================================================================
# CONFIGURATION AND SETUP
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Save command line environment variables before loading config files
CMDLINE_DRY_RUN="${DRY_RUN:-}"
CMDLINE_VERBOSE="${VERBOSE:-}"
CMDLINE_DEBUG="${DEBUG_MODE:-}"

# Load main environment configuration
if [[ -f ".env" ]]; then
    source .env
    echo "✅ Loaded main configuration from .env"
else
    echo "❌ .env file not found. Please create it first."
    exit 1
fi

# Load download-specific configuration
if [[ -f "download_config.env" ]]; then
    source download_config.env
    echo "✅ Loaded download configuration from download_config.env"
else
    echo "❌ download_config.env file not found. Please create it first."
    exit 1
fi

# Restore command line overrides
if [[ -n "$CMDLINE_DRY_RUN" ]]; then
    DRY_RUN="$CMDLINE_DRY_RUN"
    echo "🔧 Command line override: DRY_RUN=$DRY_RUN"
fi

if [[ -n "$CMDLINE_VERBOSE" ]]; then
    VERBOSE="$CMDLINE_VERBOSE"
    echo "🔧 Command line override: VERBOSE=$VERBOSE"
fi

if [[ -n "$CMDLINE_DEBUG" ]]; then
    DEBUG_MODE="$CMDLINE_DEBUG"
    echo "🔧 Command line override: DEBUG_MODE=$DEBUG_MODE"
fi

# Set defaults if not specified in config files
DATA_SOURCES_CSV=${DATA_SOURCES_CSV:-"data_sources.csv"}
LOCAL_DATA_DIR=${LOCAL_DATA_DIR:-"../data"}
DOWNLOAD_LOG=${DOWNLOAD_LOG:-"logs/download.log"}
SHOW_PROGRESS_BAR=${SHOW_PROGRESS_BAR:-"true"}
S3_MAX_CONCURRENT_REQUESTS=${S3_MAX_CONCURRENT_REQUESTS:-10}
RCLONE_TRANSFERS=${RCLONE_TRANSFERS:-8}

# Create directories
mkdir -p "$(dirname "$DOWNLOAD_LOG")"
mkdir -p "$LOCAL_DATA_DIR"
mkdir -p "${TEMP_DOWNLOAD_DIR:-/tmp/energy-downloads}"

# =============================================================================
# LOGGING AND PROGRESS FUNCTIONS
# =============================================================================

log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local color=""
    
    case $level in
        "ERROR") color=$RED ;;
        "WARN")  color=$YELLOW ;;
        "INFO")  color=$GREEN ;;
        "DEBUG") color=$BLUE ;;
        *)       color=$NC ;;
    esac
    
    if [[ "$USE_COLORS" == "true" ]]; then
        echo -e "${color}[$timestamp] [$level]${NC} $message" | tee -a "$DOWNLOAD_LOG"
    else
        echo "[$timestamp] [$level] $message" | tee -a "$DOWNLOAD_LOG"
    fi
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }
log_debug() { [[ "$DEBUG_MODE" == "true" ]] && log "DEBUG" "$@" || true; }

# Progress bar function
show_progress() {
    local current=$1
    local total=$2
    local description="$3"
    local rate="${4:-}"
    local eta="${5:-}"
    
    if [[ "$SHOW_PROGRESS_BAR" != "true" ]]; then
        return 0
    fi
    
    local width=${PROGRESS_BAR_WIDTH:-50}
    local percent=$((current * 100 / total))
    local filled=$((current * width / total))
    local empty=$((width - filled))
    
    # Create progress bar
    local bar=""
    for ((i=0; i<filled; i++)); do bar+="█"; done
    for ((i=0; i<empty; i++)); do bar+="░"; done
    
    # Format output
    local output=""
    if [[ "$USE_COLORS" == "true" ]]; then
        output="${CYAN}[$bar]${NC} ${percent}% ${description}"
    else
        output="[$bar] ${percent}% ${description}"
    fi
    
    if [[ -n "$rate" && "$SHOW_TRANSFER_RATE" == "true" ]]; then
        output+=" | ${rate}"
    fi
    
    if [[ -n "$eta" && "$SHOW_ETA" == "true" ]]; then
        output+=" | ETA: ${eta}"
    fi
    
    # Print with carriage return to overwrite previous line
    printf "\r%s" "$output"
    
    # New line when complete
    if [[ $current -eq $total ]]; then
        echo ""
    fi
}

# =============================================================================
# DATA SOURCE MANAGEMENT
# =============================================================================

load_data_sources() {
    if [[ ! -f "$DATA_SOURCES_CSV" ]]; then
        log_error "Data sources CSV file not found: $DATA_SOURCES_CSV"
        exit 1
    fi
    
    log_info "📋 Loading data sources from $DATA_SOURCES_CSV"
    
    # Validate CSV format
    local header=$(head -n 1 "$DATA_SOURCES_CSV")
    local expected="project_name,project_type,data_type,source_type,url,local_path,description,priority,estimated_size_gb"
    
    if [[ "$header" != "$expected" ]]; then
        log_error "Invalid CSV header format in $DATA_SOURCES_CSV"
        log_error "Expected: $expected"
        log_error "Found: $header"
        exit 1
    fi
    
    local total_sources=$(tail -n +2 "$DATA_SOURCES_CSV" | wc -l)
    log_info "📊 Found $total_sources data sources"
    
    # Calculate total estimated size
    local total_size=$(tail -n +2 "$DATA_SOURCES_CSV" | cut -d',' -f9 | awk '{sum += $1} END {print sum}')
    log_info "📦 Total estimated size: ${total_size} GB"
}

get_project_sources() {
    local project_name="$1"
    
    # Get all sources for the specified project
    tail -n +2 "$DATA_SOURCES_CSV" | grep "^${project_name}," || true
}

get_sources_by_priority() {
    local priority="$1"
    
    # Get all sources with the specified priority
    tail -n +2 "$DATA_SOURCES_CSV" | grep ",${priority}," || true
}

# =============================================================================
# ENHANCED DOWNLOAD FUNCTIONS
# =============================================================================

download_s3_enhanced() {
    local url="$1"
    local local_path="$2"
    local description="$3"
    local estimated_size="${4:-0}"
    
    log_info "📥 Downloading S3: $description"
    log_debug "S3 URL: $url"
    log_debug "Local path: $local_path"
    log_debug "Estimated size: ${estimated_size} GB"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would download: $url -> $local_path"
        log_info "[DRY RUN] Would use multipart download with ${S3_MAX_CONCURRENT_REQUESTS} concurrent requests"
        return 0
    fi
    
    # Create local directory
    mkdir -p "$local_path"
    
    # Check available disk space
    check_disk_space "$estimated_size"
    
    # Try AWS CLI with enhanced settings first
    if command -v aws &> /dev/null; then
        log_info "🚀 Using AWS CLI with multipart/threaded download..."
        
        # Configure AWS CLI for high performance
        aws configure set default.s3.max_concurrent_requests "$S3_MAX_CONCURRENT_REQUESTS"
        aws configure set default.s3.multipart_threshold "$S3_MULTIPART_THRESHOLD"
        aws configure set default.s3.multipart_chunksize "$S3_MULTIPART_CHUNKSIZE"
        aws configure set default.s3.max_attempts "$S3_MAX_ATTEMPTS"
        aws configure set default.s3.retry_mode "$S3_RETRY_MODE"
        
        if [[ -n "$S3_MAX_BANDWIDTH" ]]; then
            aws configure set default.s3.max_bandwidth "$S3_MAX_BANDWIDTH"
        fi
        
        local aws_opts=(
            --region "${AWS_REGION:-us-east-1}"
        )
        
        # Add progress display for AWS CLI
        if [[ "$SHOW_PROGRESS_BAR" == "true" ]]; then
            aws_opts+=(--cli-read-timeout 0 --cli-connect-timeout 60)
        fi
        
        if [[ "$VERBOSE" != "true" ]]; then
            aws_opts+=(--no-cli-pager)
        fi
        
        # Execute download with progress monitoring
        if download_with_progress "aws s3 sync \"$url\" \"$local_path\" ${aws_opts[*]}" "$description"; then
            log_info "✅ Successfully downloaded with AWS CLI: $description"
            return 0
        else
            log_warn "⚠️ AWS CLI download failed, trying rclone..."
        fi
    fi
    
    # Fallback to enhanced rclone
    download_s3_with_rclone_enhanced "$url" "$local_path" "$description"
}

download_s3_with_rclone_enhanced() {
    local url="$1"
    local local_path="$2"
    local description="$3"
    
    log_info "🔄 Using enhanced rclone for S3 download..."
    
    # Parse S3 URL
    local bucket_and_path=${url#s3://}
    local bucket=${bucket_and_path%%/*}
    local path=${bucket_and_path#*/}
    
    # Configure S3 remote if needed
    local s3_remote="s3"
    if ! rclone listremotes | grep -q "^${s3_remote}:$"; then
        log_info "⚙️ Configuring S3 remote..."
        rclone config create "$s3_remote" s3 \
            provider=AWS \
            region="${AWS_REGION:-us-east-1}" \
            --non-interactive
    fi
    
    # Enhanced rclone options
    local rclone_opts=(
        --transfers="$RCLONE_TRANSFERS"
        --checkers="$RCLONE_CHECKERS"
        --buffer-size="$RCLONE_BUFFER_SIZE"
        --stats=1s
        --stats-one-line
    )
    
    if [[ "$SKIP_EXISTING_FILES" == "true" ]]; then
        rclone_opts+=(--ignore-existing)
    fi
    
    if [[ "$SHOW_PROGRESS_BAR" == "true" ]]; then
        rclone_opts+=(--progress)
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        rclone_opts+=(--verbose)
    fi
    
    # Execute rclone with progress monitoring
    if rclone copy "${s3_remote}:${bucket}/${path}" "$local_path" "${rclone_opts[@]}"; then
        log_info "✅ Successfully downloaded with rclone: $description"
        return 0
    else
        log_error "❌ Failed to download: $description"
        return 1
    fi
}

download_https_enhanced() {
    local url="$1"
    local local_path="$2"
    local description="$3"
    local estimated_size="${4:-0}"
    
    log_info "🌐 Downloading HTTPS: $description"
    log_debug "URL: $url"
    log_debug "Local path: $local_path"
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would download: $url -> $local_path"
        return 0
    fi
    
    # Create local directory
    mkdir -p "$local_path"
    
    # Check available disk space
    check_disk_space "$estimated_size"
    
    # Extract filename from URL
    local filename=$(basename "$url")
    local full_local_path="$local_path/$filename"
    
    # Skip if file exists
    if [[ "$SKIP_EXISTING_FILES" == "true" && -f "$full_local_path" ]]; then
        log_info "⏭️ Skipping existing file: $filename"
        return 0
    fi
    
    # Enhanced wget options
    local wget_opts=(
        --timeout="$WGET_TIMEOUT"
        --tries="$WGET_RETRIES"
        --continue
        --show-progress
        --progress=bar:force:noscroll
    )
    
    if [[ "$VERBOSE" != "true" ]]; then
        wget_opts+=(--quiet --show-progress)
    fi
    
    # Execute download
    log_info "⬇️ Starting download: $filename"
    if wget "${wget_opts[@]}" -O "$full_local_path" "$url"; then
        log_info "✅ Successfully downloaded: $description"
        return 0
    else
        log_error "❌ Failed to download: $description"
        return 1
    fi
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

check_disk_space() {
    local required_gb="$1"
    
    if [[ "$required_gb" == "0" ]]; then
        return 0
    fi
    
    local available_gb=$(df "$LOCAL_DATA_DIR" | tail -1 | awk '{print int($4/1024/1024)}')
    local min_required=$((required_gb + MIN_FREE_SPACE_GB))
    
    if [[ $available_gb -lt $min_required ]]; then
        log_error "❌ Insufficient disk space!"
        log_error "Required: ${min_required} GB, Available: ${available_gb} GB"
        exit 1
    fi
    
    log_debug "💾 Disk space check: ${available_gb} GB available, ${min_required} GB required"
}

download_with_progress() {
    local command="$1"
    local description="$2"
    
    log_debug "Executing: $command"
    
    # Execute command and capture output for progress parsing
    if [[ "$SHOW_PROGRESS_BAR" == "true" ]]; then
        # Run command in background and monitor progress
        eval "$command" &
        local pid=$!
        
        # Monitor progress (simplified - in real implementation would parse actual progress)
        local count=0
        while kill -0 $pid 2>/dev/null; do
            show_progress $count 100 "$description" "Downloading..." "Calculating..."
            sleep 1
            count=$((count + 2))
            if [[ $count -gt 100 ]]; then count=100; fi
        done
        
        # Wait for command to complete and get exit status
        wait $pid
        local exit_status=$?
        
        if [[ $exit_status -eq 0 ]]; then
            show_progress 100 100 "$description" "Complete" "Done"
        fi
        
        return $exit_status
    else
        # Execute command normally without progress monitoring
        eval "$command"
    fi
}

# =============================================================================
# PROJECT DOWNLOAD FUNCTIONS
# =============================================================================

download_project() {
    local project_name="$1"
    
    log_info "🎯 Starting download for project: $project_name"
    
    # Get all sources for this project
    local sources=$(get_project_sources "$project_name")
    
    if [[ -z "$sources" ]]; then
        log_error "❌ No data sources found for project: $project_name"
        return 1
    fi
    
    local total_sources=$(echo "$sources" | wc -l)
    local current_source=0
    local failed_downloads=()
    
    log_info "📊 Found $total_sources data sources for $project_name"
    
    # Process each source
    while IFS=',' read -r proj_name proj_type data_type source_type url local_path desc priority size_gb; do
        current_source=$((current_source + 1))
        
        log_info "📦 [$current_source/$total_sources] Processing: $desc"
        
        # Build full local path
        local full_local_path="$LOCAL_DATA_DIR/$proj_type/$proj_name/$local_path"
        
        # Download based on source type
        case "$source_type" in
            "s3")
                if ! download_s3_enhanced "$url" "$full_local_path" "$desc" "$size_gb"; then
                    failed_downloads+=("$desc")
                fi
                ;;
            "https")
                if ! download_https_enhanced "$url" "$full_local_path" "$desc" "$size_gb"; then
                    failed_downloads+=("$desc")
                fi
                ;;
            *)
                log_error "❌ Unknown source type: $source_type"
                failed_downloads+=("$desc")
                ;;
        esac
        
        # Show overall progress
        show_progress $current_source $total_sources "Project $project_name"
        
    done <<< "$sources"
    
    # Report results
    if [[ ${#failed_downloads[@]} -eq 0 ]]; then
        log_info "🎉 Successfully downloaded all data for $project_name"
        
        # Backup to Google Drive if enabled
        if [[ "$BACKUP_TO_GDRIVE" == "true" && "$BACKUP_AFTER_EACH_PROJECT" == "true" ]]; then
            backup_project_to_gdrive "$project_name"
        fi
        
        return 0
    else
        log_error "❌ Failed downloads for $project_name: ${failed_downloads[*]}"
        return 1
    fi
}

backup_project_to_gdrive() {
    local project_name="$1"
    
    log_info "☁️ Backing up $project_name to Google Drive..."
    
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY RUN] Would backup project to Google Drive"
        return 0
    fi
    
    # Implementation would go here
    log_info "✅ Backup completed for $project_name"
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    local start_time=$(date +%s)
    
    log_info "🚀 Starting Enhanced Energy Inspection 3D data download"
    log_info "⚙️ Configuration: DRY_RUN=$DRY_RUN, PROGRESS=$SHOW_PROGRESS_BAR"
    
    # Load and validate data sources
    load_data_sources
    
    # Create directory structure
    log_info "📁 Creating directory structure..."
    
    # Get unique project types and names from CSV
    local projects=$(tail -n +2 "$DATA_SOURCES_CSV" | cut -d',' -f1,2 | sort -u)
    
    while IFS=',' read -r proj_name proj_type; do
        mkdir -p "$LOCAL_DATA_DIR/$proj_type/$proj_name"
    done <<< "$projects"
    
    log_info "✅ Directory structure created"
    
    # Download projects based on priority or specific project
    local failed_projects=()
    
    if [[ $# -eq 1 ]]; then
        # Download specific project
        local target_project="$1"
        if ! download_project "$target_project"; then
            failed_projects+=("$target_project")
        fi
    else
        # Download all projects by priority
        for priority in "high" "medium" "low"; do
            log_info "🎯 Processing $priority priority projects..."
            
            local priority_projects=$(get_sources_by_priority "$priority" | cut -d',' -f1 | sort -u)
            
            while read -r project_name; do
                if [[ -n "$project_name" ]]; then
                    if ! download_project "$project_name"; then
                        failed_projects+=("$project_name")
                    fi
                fi
            done <<< "$priority_projects"
        done
    fi
    
    # Final summary
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_info "⏱️ Total download time: ${duration}s"
    
    if [[ ${#failed_projects[@]} -eq 0 ]]; then
        log_info "🎉 All downloads completed successfully!"
    else
        log_error "❌ Failed projects: ${failed_projects[*]}"
        exit 1
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-all}" in
    "castro"|"Castro")
        main "Castro"
        ;;
    "mudjar"|"Mudjar")
        main "Mudjar"
        ;;
    "giorgio"|"Giorgio")
        main "Giorgio"
        ;;
    "mccarthy"|"McCarthy")
        main "McCarthy"
        ;;
    "rpcs"|"RPCS")
        main "RPCS"
        ;;
    "res"|"RES")
        main "RES"
        ;;
    "all"|*)
        main
        ;;
esac

log_info "✅ Script execution completed"
