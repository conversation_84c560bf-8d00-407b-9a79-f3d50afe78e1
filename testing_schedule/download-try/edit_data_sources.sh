#!/bin/bash

# =============================================================================
# Data Sources CSV Editor
# =============================================================================
# Simple script to help edit data source URLs and paths
# =============================================================================

set -euo pipefail

CSV_FILE="data_sources.csv"
BACKUP_DIR="backups"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }

# Create backup
create_backup() {
    mkdir -p "$BACKUP_DIR"
    local backup_file="$BACKUP_DIR/data_sources_$(date +%Y%m%d_%H%M%S).csv"
    cp "$CSV_FILE" "$backup_file"
    log_info "Backup created: $backup_file"
}

# Show current data sources
show_sources() {
    log_info "Current data sources:"
    echo ""
    
    if [[ ! -f "$CSV_FILE" ]]; then
        log_error "CSV file not found: $CSV_FILE"
        return 1
    fi
    
    # Show with line numbers for easy editing
    cat -n "$CSV_FILE" | while IFS= read -r line; do
        if [[ $line =~ ^[[:space:]]*1[[:space:]] ]]; then
            # Header line
            echo -e "${BLUE}$line${NC}"
        else
            echo "$line"
        fi
    done
    echo ""
}

# Edit specific project URLs
edit_project() {
    local project_name="$1"
    
    log_info "Editing URLs for project: $project_name"
    
    # Create backup first
    create_backup
    
    # Get current sources for project
    local sources=$(grep "^${project_name}," "$CSV_FILE" || true)
    
    if [[ -z "$sources" ]]; then
        log_error "No sources found for project: $project_name"
        return 1
    fi
    
    echo ""
    log_info "Current sources for $project_name:"
    echo "$sources" | cat -n
    echo ""
    
    # Edit each source
    local line_num=1
    while IFS=',' read -r proj_name proj_type data_type source_type url local_path desc priority size_gb; do
        echo -e "${YELLOW}[$line_num] $data_type ($source_type):${NC}"
        echo "Current URL: $url"
        echo -n "New URL (press Enter to keep current): "
        read -r new_url
        
        if [[ -n "$new_url" ]]; then
            # Replace the URL in the CSV file
            local old_line="$proj_name,$proj_type,$data_type,$source_type,$url,$local_path,$desc,$priority,$size_gb"
            local new_line="$proj_name,$proj_type,$data_type,$source_type,$new_url,$local_path,$desc,$priority,$size_gb"
            
            # Use sed to replace the line
            sed -i.bak "s|$old_line|$new_line|g" "$CSV_FILE"
            log_info "Updated $data_type URL"
        fi
        
        line_num=$((line_num + 1))
        echo ""
    done <<< "$sources"
    
    log_info "✅ Project $project_name updated successfully"
}

# Validate CSV format
validate_csv() {
    log_info "Validating CSV format..."
    
    if [[ ! -f "$CSV_FILE" ]]; then
        log_error "CSV file not found: $CSV_FILE"
        return 1
    fi
    
    # Check header
    local header=$(head -n 1 "$CSV_FILE")
    local expected="project_name,project_type,data_type,source_type,url,local_path,description,priority,estimated_size_gb"
    
    if [[ "$header" != "$expected" ]]; then
        log_error "Invalid CSV header format"
        log_error "Expected: $expected"
        log_error "Found: $header"
        return 1
    fi
    
    # Check each data line
    local line_num=2
    local errors=0
    
    tail -n +2 "$CSV_FILE" | while IFS=',' read -r proj_name proj_type data_type source_type url local_path desc priority size_gb; do
        # Check required fields
        if [[ -z "$proj_name" || -z "$proj_type" || -z "$data_type" || -z "$source_type" || -z "$url" ]]; then
            log_error "Line $line_num: Missing required fields"
            errors=$((errors + 1))
        fi
        
        # Check source type
        if [[ "$source_type" != "s3" && "$source_type" != "https" ]]; then
            log_error "Line $line_num: Invalid source_type '$source_type' (must be 's3' or 'https')"
            errors=$((errors + 1))
        fi
        
        # Check URL format
        if [[ "$source_type" == "s3" && ! "$url" =~ ^s3:// ]]; then
            log_error "Line $line_num: S3 URL must start with 's3://'"
            errors=$((errors + 1))
        elif [[ "$source_type" == "https" && ! "$url" =~ ^https:// ]]; then
            log_error "Line $line_num: HTTPS URL must start with 'https://'"
            errors=$((errors + 1))
        fi
        
        line_num=$((line_num + 1))
    done
    
    if [[ $errors -eq 0 ]]; then
        log_info "✅ CSV validation passed"
        return 0
    else
        log_error "❌ CSV validation failed with $errors errors"
        return 1
    fi
}

# Add new data source
add_source() {
    log_info "Adding new data source..."
    
    echo -n "Project name: "
    read -r proj_name
    
    echo -n "Project type (ENEL/USA): "
    read -r proj_type
    
    echo -n "Data type (pointcloud/cad/ortho): "
    read -r data_type
    
    echo -n "Source type (s3/https): "
    read -r source_type
    
    echo -n "URL: "
    read -r url
    
    echo -n "Local path (e.g., pointcloud, cad, ortho): "
    read -r local_path
    
    echo -n "Description: "
    read -r desc
    
    echo -n "Priority (high/medium/low): "
    read -r priority
    
    echo -n "Estimated size (GB): "
    read -r size_gb
    
    # Create backup first
    create_backup
    
    # Add new line to CSV
    echo "$proj_name,$proj_type,$data_type,$source_type,$url,$local_path,$desc,$priority,$size_gb" >> "$CSV_FILE"
    
    log_info "✅ New data source added"
}

# Show menu
show_menu() {
    echo ""
    echo "=== Data Sources CSV Editor ==="
    echo ""
    echo "1) Show current data sources"
    echo "2) Edit project URLs"
    echo "3) Add new data source"
    echo "4) Validate CSV format"
    echo "5) Open CSV in editor"
    echo "6) Restore from backup"
    echo "7) Exit"
    echo ""
}

# Restore from backup
restore_backup() {
    log_info "Available backups:"
    ls -la "$BACKUP_DIR"/*.csv 2>/dev/null || {
        log_warn "No backups found"
        return 1
    }
    
    echo -n "Enter backup filename to restore: "
    read -r backup_file
    
    if [[ -f "$BACKUP_DIR/$backup_file" ]]; then
        cp "$BACKUP_DIR/$backup_file" "$CSV_FILE"
        log_info "✅ Restored from backup: $backup_file"
    else
        log_error "Backup file not found: $backup_file"
    fi
}

# Main menu loop
main() {
    while true; do
        show_menu
        echo -n "Choose an option (1-7): "
        read -r choice
        
        case $choice in
            1)
                show_sources
                ;;
            2)
                echo -n "Enter project name to edit: "
                read -r project_name
                edit_project "$project_name"
                ;;
            3)
                add_source
                ;;
            4)
                validate_csv
                ;;
            5)
                if command -v nano &> /dev/null; then
                    nano "$CSV_FILE"
                elif command -v vim &> /dev/null; then
                    vim "$CSV_FILE"
                else
                    log_warn "No editor found. Please edit $CSV_FILE manually."
                fi
                ;;
            6)
                restore_backup
                ;;
            7)
                log_info "Goodbye!"
                exit 0
                ;;
            *)
                log_error "Invalid choice. Please enter 1-7."
                ;;
        esac
        
        echo ""
        echo "Press Enter to continue..."
        read -r
    done
}

# Handle command line arguments
case "${1:-menu}" in
    "show")
        show_sources
        ;;
    "edit")
        if [[ $# -lt 2 ]]; then
            log_error "Usage: $0 edit <project_name>"
            exit 1
        fi
        edit_project "$2"
        ;;
    "validate")
        validate_csv
        ;;
    "add")
        add_source
        ;;
    "menu"|*)
        main
        ;;
esac
