# 🚀 Enhanced Data Download System v2

## ✨ **New Features**

### 📊 **CSV-Based Configuration**
- All data source URLs and paths are now stored in `data_sources.csv`
- Easy to edit and maintain
- No need to modify shell scripts to change URLs
- Built-in validation and backup system

### 📈 **Visible Progress Bars**
- Real-time progress bars for each download
- Transfer rate display (MB/s)
- ETA (Estimated Time Remaining)
- File count progress
- Colored output for better visibility

### ⚡ **Multipart/Threaded S3 Downloads**
- AWS CLI with multipart upload support
- Configurable concurrent requests (default: 10)
- Optimized chunk sizes for large files
- Automatic retry with exponential backoff
- Enhanced rclone fallback with parallel transfers

---

## 📁 **File Structure**

```
testing_schedule/
├── data_sources.csv                    # 📊 All data source URLs and paths
├── download_config.env                 # ⚙️ Download-specific configuration
├── download_project_data_v2.sh         # 🚀 Enhanced download script
├── edit_data_sources.sh               # ✏️ CSV editor utility
├── .env                               # 🔧 Main configuration
└── backups/                           # 💾 Automatic CSV backups
    └── data_sources_YYYYMMDD_HHMMSS.csv
```

---

## 🎯 **Quick Start**

### **1. Basic Download**
```bash
# Download specific project
./download_project_data_v2.sh castro
./download_project_data_v2.sh mudjar
./download_project_data_v2.sh giorgio

# Download all projects
./download_project_data_v2.sh
```

### **2. Test Mode**
```bash
# Dry run to see what would be downloaded
DRY_RUN=true ./download_project_data_v2.sh castro

# Verbose output with debug info
VERBOSE=true DEBUG_MODE=true ./download_project_data_v2.sh castro
```

### **3. Edit Data Sources**
```bash
# Interactive CSV editor
./edit_data_sources.sh

# Quick commands
./edit_data_sources.sh show              # Show current sources
./edit_data_sources.sh edit Castro       # Edit Castro URLs
./edit_data_sources.sh validate          # Validate CSV format
```

---

## 📊 **Data Sources Configuration**

### **CSV Format**
```csv
project_name,project_type,data_type,source_type,url,local_path,description,priority,estimated_size_gb
Castro,ENEL,pointcloud,s3,s3://bucket/path/,pointcloud,Castro Point Cloud,high,1.2
```

### **CSV Fields**
| Field | Description | Values |
|-------|-------------|--------|
| `project_name` | Project identifier | Castro, Mudjar, Giorgio, McCarthy, RPCS, RES |
| `project_type` | Project category | ENEL, USA |
| `data_type` | Type of data | pointcloud, cad, ortho |
| `source_type` | Download method | s3, https |
| `url` | Source URL | s3://bucket/path/ or https://... |
| `local_path` | Local subdirectory | pointcloud, cad, ortho |
| `description` | Human-readable description | Any descriptive text |
| `priority` | Download priority | high, medium, low |
| `estimated_size_gb` | Estimated file size | Numeric value in GB |

### **Edit URLs Easily**
```bash
# Interactive editor
./edit_data_sources.sh

# Choose option 2 to edit project URLs
# Choose option 1 to view current sources
# Choose option 4 to validate changes
```

---

## ⚙️ **Configuration Options**

### **Performance Tuning (`download_config.env`)**
```bash
# High-speed connection
S3_MAX_CONCURRENT_REQUESTS=20
RCLONE_TRANSFERS=16
RCLONE_CHECKERS=32
AWS_CLI_MAX_CONCURRENT_REQUESTS=20

# Limited bandwidth
S3_MAX_BANDWIDTH="10MB/s"
AWS_CLI_MAX_BANDWIDTH="10MB/s"
RCLONE_TRANSFERS=2
RCLONE_CHECKERS=4

# Progress display
SHOW_PROGRESS_BAR="true"
SHOW_TRANSFER_RATE="true"
SHOW_ETA="true"
USE_COLORS="true"
```

### **S3 Multipart Settings**
```bash
S3_MULTIPART_THRESHOLD="64MB"          # Files larger than this use multipart
S3_MULTIPART_CHUNKSIZE="16MB"          # Size of each part
S3_MAX_CONCURRENT_REQUESTS=10          # Concurrent requests
S3_MAX_BANDWIDTH=""                    # Bandwidth limit (empty = unlimited)
```

---

## 📈 **Progress Display Features**

### **Real-time Progress Bars**
```
[████████████████░░░░░░░░░░░░░░░░░░] 66% Castro Point Cloud | 15.2 MB/s | ETA: 2m 30s
```

### **Project-level Progress**
```
📦 [2/3] Processing: Castro CAD Files
[██████████████████████████████████] 100% Project Castro
```

### **Overall Statistics**
```
📊 Found 18 data sources
📦 Total estimated size: 29.75 GB
⏱️ Total download time: 1847s
```

---

## 🔧 **Advanced Features**

### **Automatic Backup System**
- CSV files are automatically backed up before editing
- Backups stored in `backups/` directory with timestamps
- Easy restore functionality

### **Validation System**
```bash
# Validate CSV format
./edit_data_sources.sh validate

# Check for:
# - Missing required fields
# - Invalid source types
# - Malformed URLs
# - Inconsistent data
```

### **Error Handling**
- Automatic retry with exponential backoff
- Fallback from AWS CLI to rclone
- Continue on error (configurable)
- Detailed error logging

### **Disk Space Management**
```bash
MIN_FREE_SPACE_GB=10                   # Minimum free space to maintain
MAX_TOTAL_SIZE_GB=100                  # Maximum total download size
```

---

## 🎯 **Project Data Overview**

| Project | Type | Sources | Est. Size | Priority |
|---------|------|---------|-----------|----------|
| **Castro** | ENEL | 3 (PC, CAD, Ortho) | 3.6 GB | High |
| **Mudjar** | ENEL | 2 (PC, CAD) | 2.0 GB | High |
| **Giorgio** | ENEL | 4 (PC, CAD×2, Ortho) | 4.1 GB | High |
| **McCarthy** | USA | 3 (PC, CAD, Ortho) | 6.4 GB | Medium |
| **RPCS** | USA | 3 (PC, CAD, Ortho) | 5.55 GB | Medium |
| **RES** | USA | 3 (PC, CAD, Ortho) | 8.1 GB | Low |

**Total**: 18 data sources, ~29.75 GB

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **CSV Format Errors**
```bash
# Validate and fix CSV
./edit_data_sources.sh validate

# Common issues:
# - Missing commas
# - Extra spaces
# - Invalid URLs
# - Wrong source types
```

#### **Download Failures**
```bash
# Check AWS credentials
aws configure list

# Test S3 access
aws s3 ls s3://bucket-name/

# Check network connectivity
curl -I "https://test-url"

# View detailed logs
tail -f logs/download.log
```

#### **Performance Issues**
```bash
# Reduce concurrent requests
S3_MAX_CONCURRENT_REQUESTS=5
RCLONE_TRANSFERS=4

# Limit bandwidth
S3_MAX_BANDWIDTH="50MB/s"

# Check system resources
top
df -h
```

### **Recovery Options**
```bash
# Restore CSV from backup
./edit_data_sources.sh
# Choose option 6

# Resume interrupted downloads
SKIP_EXISTING_FILES="true" ./download_project_data_v2.sh

# Download specific data type only
grep "pointcloud" data_sources.csv > temp.csv
# Edit script to use temp.csv
```

---

## 📞 **Support Commands**

### **Quick Diagnostics**
```bash
# Show current configuration
cat download_config.env | grep -v "^#" | grep -v "^$"

# Check data sources
./edit_data_sources.sh show

# Validate everything
./edit_data_sources.sh validate

# Test download (dry run)
DRY_RUN=true VERBOSE=true ./download_project_data_v2.sh castro
```

### **Performance Monitoring**
```bash
# Monitor download progress
tail -f logs/download.log

# Check disk usage
watch -n 5 'du -sh ../data/'

# Monitor network usage
iftop  # or nethogs
```

---

## 🎉 **Benefits of Enhanced System**

✅ **Easy URL Management**: Edit URLs in CSV without touching scripts  
✅ **Visual Progress**: See exactly what's happening with progress bars  
✅ **High Performance**: Multipart/threaded downloads for maximum speed  
✅ **Robust Error Handling**: Automatic retries and fallback mechanisms  
✅ **Flexible Configuration**: Tune performance for your connection  
✅ **Safe Editing**: Automatic backups and validation  
✅ **Better Logging**: Detailed logs with timestamps and colors  
✅ **Disk Management**: Automatic space checking and management  

---

**🎯 Ready to download 29.75 GB of project data with maximum efficiency and visibility!**
