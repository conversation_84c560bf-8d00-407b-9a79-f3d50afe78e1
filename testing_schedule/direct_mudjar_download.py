#!/usr/bin/env python3
"""
Direct Mudjar Download - Cherry-pick Essential Files Only

Based on data_sources.csv, Mudjar has a direct HTTPS link to a single point cloud file.
This is perfect for thesis - no massive folder downloads needed.
"""

import subprocess
import requests
from pathlib import Path
import argparse

# MUDJAR ESSENTIAL FILES (from data_sources.csv)
MUDJAR_ESSENTIALS = {
    'pointcloud': {
        'url': 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las',
        'estimated_size_gb': 1.4,
        'description': 'Mudjar Point Cloud - Single LAS file'
    },
    'cad_essential': {
        'url': 's3://ftp-enel/mudejar-spain/2024-10-22/CAD/gre.eec.d.21.es.p.11730.00.149.04.dwg',
        'estimated_size_mb': 36.4,
        'description': 'Mudjar CAD - Essential DWG file only'
    }
}

def check_file_size(url: str) -> float:
    """Check actual file size without downloading"""
    try:
        response = requests.head(url, timeout=30)
        if response.status_code == 200:
            size_bytes = int(response.headers.get('content-length', 0))
            size_gb = size_bytes / (1024**3)
            return size_gb
        else:
            print(f"Warning: Could not check size for {url} (status: {response.status_code})")
            return 0.0
    except Exception as e:
        print(f"Error checking size for {url}: {e}")
        return 0.0

def download_file_direct(url: str, output_path: str, description: str) -> bool:
    """Download a single file directly"""
    
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    print(f"📥 Downloading {description}...")
    print(f"   URL: {url}")
    print(f"   Output: {output_path}")
    
    try:
        # Use curl for reliable download with progress
        result = subprocess.run([
            'curl', '-L', '--progress-bar', 
            '-o', str(output_file),
            url
        ], check=True)
        
        # Check if file was downloaded
        if output_file.exists():
            size_mb = output_file.stat().st_size / (1024**2)
            print(f"✅ Downloaded: {size_mb:.1f} MB")
            return True
        else:
            print(f"❌ Download failed: File not created")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Download failed: {e}")
        return False

def download_s3_file(s3_url: str, output_path: str, description: str, 
                    rclone_remote: str = "datasee-s3") -> bool:
    """Download a single S3 file using rclone"""
    
    output_file = Path(output_path)
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    print(f"📥 Downloading {description}...")
    print(f"   S3 URL: {s3_url}")
    print(f"   Output: {output_path}")
    
    try:
        # Convert S3 URL to rclone format
        rclone_path = s3_url.replace("s3://", f"{rclone_remote}:")
        
        result = subprocess.run([
            'rclone', 'copy', rclone_path, str(output_file.parent),
            '--progress'
        ], check=True)
        
        # Check if file was downloaded
        if output_file.exists():
            size_mb = output_file.stat().st_size / (1024**2)
            print(f"✅ Downloaded: {size_mb:.1f} MB")
            return True
        else:
            print(f"❌ Download failed: File not created")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Download failed: {e}")
        return False

def download_mudjar_essentials(output_dir: str = "./mudjar_thesis_data") -> None:
    """Download only essential Mudjar files for thesis"""
    
    output_path = Path(output_dir)
    
    print("🎯 MUDJAR THESIS-FOCUSED DOWNLOAD")
    print("=" * 50)
    print("Downloading ONLY essential files for 12-week thesis completion")
    
    # Check point cloud file size first
    pc_info = MUDJAR_ESSENTIALS['pointcloud']
    print(f"\n🔍 Checking point cloud file size...")
    actual_size_gb = check_file_size(pc_info['url'])
    
    if actual_size_gb > 0:
        print(f"   Estimated: {pc_info['estimated_size_gb']} GB")
        print(f"   Actual: {actual_size_gb:.2f} GB")
        
        if actual_size_gb > 5.0:
            print(f"⚠️  WARNING: File is {actual_size_gb:.1f} GB - larger than thesis-safe limit")
            response = input("Continue download anyway? (y/N): ")
            if response.lower() != 'y':
                print("Skipping point cloud download")
                return
    
    # Download point cloud
    pc_output = output_path / "pointcloud" / "mudejar-spain_07-11-2024-pointcloud.las"
    success_pc = download_file_direct(
        pc_info['url'], 
        str(pc_output),
        pc_info['description']
    )
    
    # Download essential CAD file
    cad_info = MUDJAR_ESSENTIALS['cad_essential']
    cad_output = output_path / "cad" / "mudejar_essential.dwg"
    success_cad = download_s3_file(
        cad_info['url'],
        str(cad_output),
        cad_info['description']
    )
    
    # Summary
    print(f"\n📋 DOWNLOAD SUMMARY:")
    print(f"   Point Cloud: {'✅' if success_pc else '❌'}")
    print(f"   CAD File: {'✅' if success_cad else '❌'}")
    
    if success_pc:
        print(f"\n🚀 READY FOR THESIS DEVELOPMENT:")
        print(f"   Primary dataset: {pc_output}")
        print(f"   Start with: Method implementation on Mudjar point cloud")
        print(f"   Next step: Implement ICP alignment, ground segmentation")

def validate_mudjar_download(data_dir: str = "./mudjar_thesis_data") -> None:
    """Validate that Mudjar essentials are downloaded and ready"""
    
    data_path = Path(data_dir)
    
    print("🔍 MUDJAR THESIS READINESS CHECK")
    print("=" * 40)
    
    # Check point cloud
    pc_file = data_path / "pointcloud" / "mudejar-spain_07-11-2024-pointcloud.las"
    if pc_file.exists():
        size_mb = pc_file.stat().st_size / (1024**2)
        print(f"✅ Point Cloud: {size_mb:.1f} MB - READY")
        
        # Basic file validation
        if size_mb > 100:  # Should be substantial
            print(f"   File size looks reasonable for thesis work")
        else:
            print(f"⚠️  File seems small - verify download completed")
    else:
        print(f"❌ Point Cloud: Missing - Download required")
    
    # Check CAD
    cad_file = data_path / "cad" / "mudejar_essential.dwg"
    if cad_file.exists():
        size_mb = cad_file.stat().st_size / (1024**2)
        print(f"✅ CAD File: {size_mb:.1f} MB - READY")
    else:
        print(f"❌ CAD File: Missing - Download required")
    
    # Thesis readiness assessment
    if pc_file.exists():
        print(f"\n🎓 THESIS STATUS: READY TO START")
        print(f"   ✅ Can begin method development immediately")
        print(f"   ✅ Sufficient data for comparative analysis")
        print(f"   ✅ Manageable size for 12-week timeline")
    else:
        print(f"\n🎓 THESIS STATUS: NOT READY")
        print(f"   ❌ Need point cloud data to start")

def show_mudjar_strategy():
    """Show the Mudjar-focused thesis strategy"""
    
    print("🎯 MUDJAR-FIRST THESIS STRATEGY")
    print("=" * 40)
    print("Focus on single dataset for rapid method development")
    
    print(f"\n📊 MUDJAR ADVANTAGES:")
    print(f"   ✅ Single point cloud file (not folder of files)")
    print(f"   ✅ Manageable size (~1.4 GB estimated)")
    print(f"   ✅ Direct HTTPS download (no S3 complexity)")
    print(f"   ✅ ENEL project (has CAD reference)")
    
    print(f"\n⚡ RAPID DEVELOPMENT PLAN:")
    print(f"   Week 4: Download Mudjar + implement ICP on this dataset")
    print(f"   Week 5: Implement all other methods on Mudjar")
    print(f"   Week 6: Add RPCS for cross-validation")
    print(f"   Week 7-8: Complete comparative analysis")
    
    print(f"\n🎓 THESIS VALUE:")
    print(f"   📈 Rigorous method comparison on real construction data")
    print(f"   📊 Statistical validation across multiple approaches")
    print(f"   ⚡ Achievable within 12-week academic timeline")

def main():
    parser = argparse.ArgumentParser(description="Direct Mudjar download for thesis")
    parser.add_argument("command", 
                       choices=['download', 'validate', 'strategy', 'check-size'],
                       help="Command to run")
    parser.add_argument("--output-dir", default="./mudjar_thesis_data",
                       help="Output directory")
    
    args = parser.parse_args()
    
    if args.command == 'strategy':
        show_mudjar_strategy()
        
    elif args.command == 'check-size':
        pc_info = MUDJAR_ESSENTIALS['pointcloud']
        print(f"🔍 Checking Mudjar point cloud size...")
        actual_size = check_file_size(pc_info['url'])
        print(f"   Estimated: {pc_info['estimated_size_gb']} GB")
        print(f"   Actual: {actual_size:.2f} GB")
        
        if actual_size <= 2.0:
            print(f"✅ THESIS-SAFE: Size is manageable")
        else:
            print(f"⚠️  CAUTION: Larger than expected")
            
    elif args.command == 'download':
        download_mudjar_essentials(args.output_dir)
        
    elif args.command == 'validate':
        validate_mudjar_download(args.output_dir)

if __name__ == "__main__":
    main()
