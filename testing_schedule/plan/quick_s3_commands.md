# Quick S3 Exploration Commands for Thesis

## IMMEDIATE COMMANDS TO RUN

### 1. Mudjar CAD Structure (currently downloading 144GB)
```bash
# Quick overview - see what's actually in there
aws s3 ls s3://ftp-enel/mudejar-spain/ --recursive --human-readable | head -20

# Find point cloud files specifically
aws s3 ls s3://ftp-enel/mudejar-spain/ --recursive | grep -i "\.las$"

# Find GeoTIFF files
aws s3 ls s3://ftp-enel/mudejar-spain/ --recursive | grep -i "\.tif$"

# Find KML files
aws s3 ls s3://ftp-enel/mudejar-spain/ --recursive | grep -i "\.kml$"

# Find DWG files (CAD)
aws s3 ls s3://ftp-enel/mudejar-spain/ --recursive | grep -i "\.dwg$"

# See file types distribution
aws s3 ls s3://ftp-enel/mudejar-spain/ --recursive | grep -o '\.[^.]*$' | sort | uniq -c | sort -nr
```

### 2. <PERSON> Point Cloud (46.8GB confirmed)
```bash
# See the 4 large LAS files we know about
aws s3 ls s3://preetam-filezilla-test/Castro/Pointcloud/ --human-readable

# Check Castro Ortho folder
aws s3 ls s3://preetam-filezilla-test/Castro/Ortho/ --recursive --human-readable
```

### 3. Other Projects Quick Check
```bash
# RPCS (backup dataset)
aws s3 ls s3://ftp-upload-images/Data\ files\ to\ GIS\ Team/CPM\ \&\ CQM/2025/RES/Block11/Pointcloud/ --human-readable

# Check if there are smaller files in other locations
aws s3 ls s3://preetam-filezilla-test/ --recursive | grep -i "\.las$" | head -10
```

## TARGETED FILE DOWNLOADS

### If you find specific small files:
```bash
# Download a single DWG file (example)
aws s3 cp "s3://ftp-enel/mudejar-spain/2024-10-22/CAD/gre.eec.d.21.es.p.11730.00.149.04.dwg" ./mudjar_cad.dwg

# Download a single GeoTIFF (example)
aws s3 cp "s3://path/to/specific/file.tif" ./mudjar_ortho.tif

# Download a single point cloud file (if found)
aws s3 cp "s3://path/to/specific/pointcloud.las" ./mudjar_pointcloud.las
```

## THESIS-FOCUSED STRATEGY

### What to look for:
1. **Point Cloud Files**: `.las`, `.laz`, `.pcd` (< 2GB each)
2. **GeoTIFF**: `.tif`, `.tiff` (orthomosaics, < 1GB each)  
3. **CAD Files**: `.dwg`, `.dxf`, `.ifc` (< 100MB each)
4. **KML Files**: `.kml`, `.kmz` (usually small, < 10MB)

### Red flags to avoid:
- Folders with 1000+ files
- Individual files > 5GB
- ZIP archives > 1GB
- Documentation folders

## IMMEDIATE ACTION PLAN

1. **Run the exploration commands above**
2. **Identify 2-3 specific files per project**
3. **Download individual files, not folders**
4. **Start method development with whatever you get**

## EXAMPLE THESIS-SAFE DOWNLOAD

If you find these files:
```bash
# Mudjar essentials (example)
aws s3 cp "s3://ftp-enel/mudejar-spain/pointcloud/mudjar.las" ./data/mudjar_pc.las
aws s3 cp "s3://ftp-enel/mudejar-spain/cad/mudjar.dwg" ./data/mudjar_cad.dwg
aws s3 cp "s3://ftp-enel/mudejar-spain/ortho/mudjar.tif" ./data/mudjar_ortho.tif

# Total download: ~2GB instead of 144GB
```

## BACKUP PLAN

If Mudjar is too complex:
```bash
# Try RPCS or McCarthy directly
aws s3 ls s3://preetam-filezilla-test/ --recursive | grep -i "rpcs\|mccarthy"
```

The key is to **explore first, download second** - understand the structure before committing to large downloads that could consume your entire thesis timeline.
