# 📅 7-Day Pipeline Testing Schedule

## 🎯 **Testing Objectives**
- Validate all pipeline stages across 6 real-world projects
- Test different data combinations (Point Cloud + CAD/Ortho)
- Ensure robustness across ENEL, McCarthy, RPCS, and RES projects
- Identify and resolve any data-specific issues
- Document performance metrics and quality assessments

## 📊 **Test Data Overview**

| Project | Type | Point Cloud | IFC | CAD | Ortho | Priority |
|---------|------|-------------|-----|-----|-------|----------|
| **Castro** | ENEL | ✅ S3 | ❌ | ✅ S3 | ✅ S3 | High |
| **Mudjar** | ENEL | ✅ HTTPS | ❌ | ✅ S3 | ✅ HTTPS | High |
| **Giorgio** | ENEL | ✅ HTTPS | ❌ | ✅ S3 | ✅ HTTPS | High |
| **McCarthy** | USA | ✅ S3 | ❌ | ✅ S3 | ✅ S3 | Medium |
| **RPCS** | USA | ✅ S3 | ❌ | ✅ S3 | ✅ S3 | Medium |
| **RES** | USA | ✅ S3 | ❌ | ✅ S3 | ✅ HTTPS | Low |

---

## 📋 **Day 1: Setup & Ground Segmentation Testing**

### **Morning (9:00-12:00): Environment Setup**
- [ ] **Data Download & Organization**
  - Download Castro point cloud data (S3)
  - Download Mudjar point cloud data (HTTPS)
  - Set up local data directory structure
  - Configure PROJECT_TYPE/PROJECT_NAME variables

- [ ] **Notebook Validation**
  - Verify all JSON syntax is valid
  - Test notebook imports and dependencies
  - Configure data paths for each project

### **Afternoon (13:00-17:00): Ground Segmentation**
**Notebook**: `notebooks/ground_segmentation/ground_removal.ipynb`

#### **Test 1.1: Castro (ENEL)**
- [ ] Load Castro point cloud from S3
- [ ] Test RANSAC ground segmentation
- [ ] Export .ply and .pcd formats
- [ ] Validate output file sizes and point counts
- [ ] **Expected Output**: `Castro_non_ground_points.ply`, `Castro_ground_points.ply`

#### **Test 1.2: Mudjar (ENEL)**
- [ ] Load Mudjar point cloud from HTTPS
- [ ] Test PMF ground segmentation
- [ ] Compare results with RANSAC
- [ ] **Expected Output**: `Mudjar_non_ground_points.ply`, `Mudjar_ground_points.ply`

#### **Test 1.3: Performance Comparison**
- [ ] Document processing times
- [ ] Compare ground/non-ground ratios
- [ ] Assess segmentation quality visually

**📝 Deliverables**: Ground segmentation results for 2 ENEL projects

---

## 📋 **Day 2: Alignment Testing**

### **Morning (9:00-12:00): CAD Data Preparation**
- [ ] **Download CAD Data**
  - Castro CAD files (S3)
  - Mudjar CAD files (S3)
  - Giorgio CAD files (S3)

- [ ] **CAD Processing**
  - Convert CAD to compatible formats
  - Extract pile positions and metadata
  - Create design reference files

### **Afternoon (13:00-17:00): Alignment Testing**
**Notebook**: `notebooks/alignment/ifc_pc_alignment_research.ipynb`

#### **Test 2.1: Castro Alignment**
- [ ] Load Castro non-ground points
- [ ] Load Castro CAD design data
- [ ] Test manual alignment workflow
- [ ] Export aligned point cloud
- [ ] **Expected Output**: `Castro_aligned_point_cloud.pcd`

#### **Test 2.2: Mudjar Alignment**
- [ ] Load Mudjar non-ground points
- [ ] Load Mudjar CAD design data
- [ ] Test ICP alignment refinement
- [ ] **Expected Output**: `Mudjar_aligned_point_cloud.pcd`

#### **Test 2.3: Alignment Quality Assessment**
- [ ] Measure alignment accuracy
- [ ] Document transformation matrices
- [ ] Validate coordinate system consistency

**📝 Deliverables**: Aligned point clouds for 2 ENEL projects

---

## 📋 **Day 3: Pile Detection Testing**

### **Morning (9:00-12:00): Giorgio Data Setup**
- [ ] **Download Giorgio Data**
  - Point cloud from HTTPS
  - CAD files from S3
  - Ortho image from HTTPS

- [ ] **Ground Segmentation for Giorgio**
  - Process Giorgio point cloud
  - Export ground-removed points

### **Afternoon (13:00-17:00): Pile Detection**
**Notebooks**: 
- `notebooks/pile_detection/foundation_pile_detection.ipynb`
- `notebooks/pile_detection/i_section_pile_detection_dgcnn.ipynb`

#### **Test 3.1: Castro Pile Detection**
- [ ] Load Castro aligned point cloud
- [ ] Test geometric clustering detection
- [ ] Classify pile types (I-section vs cylindrical)
- [ ] **Expected Output**: `Castro_detected_pile_centers.csv`

#### **Test 3.2: Mudjar Pile Detection**
- [ ] Load Mudjar aligned point cloud
- [ ] Test DGCNN-based detection
- [ ] Compare with geometric method
- [ ] **Expected Output**: `Mudjar_detected_pile_centers.csv`

#### **Test 3.3: Giorgio Pile Detection**
- [ ] Process Giorgio aligned point cloud
- [ ] Test both detection methods
- [ ] **Expected Output**: `Giorgio_detected_pile_centers.csv`

#### **Test 3.4: Detection Quality Analysis**
- [ ] Compare detection counts across projects
- [ ] Analyze confidence scores
- [ ] Validate pile type classifications

**📝 Deliverables**: Pile detection results for 3 ENEL projects

---

## 📋 **Day 4: USA Projects Setup & Testing**

### **Morning (9:00-12:00): USA Data Download**
- [ ] **McCarthy Project Setup**
  - Download point cloud (S3)
  - Download CAD files (S3)
  - Download ortho images (S3)

- [ ] **RPCS Project Setup**
  - Download point cloud (S3)
  - Download CAD files (S3)
  - Download ortho images (S3)

### **Afternoon (13:00-17:00): USA Pipeline Testing**

#### **Test 4.1: McCarthy Full Pipeline**
- [ ] Ground segmentation
- [ ] CAD alignment
- [ ] Pile detection
- [ ] **Expected Outputs**: 
  - `McCarthy_non_ground_points.ply`
  - `McCarthy_aligned_point_cloud.pcd`
  - `McCarthy_detected_pile_centers.csv`

#### **Test 4.2: RPCS Full Pipeline**
- [ ] Ground segmentation
- [ ] CAD alignment
- [ ] Pile detection
- [ ] **Expected Outputs**:
  - `RPCS_non_ground_points.ply`
  - `RPCS_aligned_point_cloud.pcd`
  - `RPCS_detected_pile_centers.csv`

#### **Test 4.3: Cross-Regional Comparison**
- [ ] Compare ENEL vs USA project results
- [ ] Analyze regional differences in data quality
- [ ] Document processing time variations

**📝 Deliverables**: Complete pipeline results for 2 USA projects

---

## 📋 **Day 5: Trench Segmentation & Compliance Analysis**

### **Morning (9:00-12:00): Trench Segmentation**
**Notebook**: `notebooks/trench_detection/trench_segmentation_unet.ipynb`

#### **Test 5.1: Ortho-based Trench Detection**
- [ ] Castro ortho processing
- [ ] Mudjar ortho processing
- [ ] Giorgio ortho processing
- [ ] **Expected Outputs**: `{Project}_trench_mask.npy`, `{Project}_trench_segmentation.ply`

#### **Test 5.2: Point Cloud-based Trench Detection**
- [ ] McCarthy ground-level analysis
- [ ] RPCS ground-level analysis
- [ ] Compare ortho vs point cloud methods

### **Afternoon (13:00-17:00): Compliance Analysis**
**Notebook**: `notebooks/compliance_analysis/compliance_analysis.ipynb`

#### **Test 5.3: Design vs Detection Comparison**
- [ ] Castro compliance analysis
- [ ] Mudjar compliance analysis
- [ ] Giorgio compliance analysis
- [ ] **Expected Outputs**: 
  - `{Project}_compliance_analysis.csv`
  - `{Project}_compliance_deviations.json`

#### **Test 5.4: USA Compliance Analysis**
- [ ] McCarthy compliance analysis
- [ ] RPCS compliance analysis
- [ ] Cross-project compliance comparison

**📝 Deliverables**: Trench segmentation and compliance results for all projects

---

## 📋 **Day 6: RES Project & Visualization Testing**

### **Morning (9:00-12:00): RES Project Complete Pipeline**
- [ ] **RES Data Download**
  - Point cloud (S3)
  - CAD files (S3)
  - Ortho image (HTTPS)

#### **Test 6.1: RES Full Pipeline**
- [ ] Ground segmentation
- [ ] CAD alignment
- [ ] Pile detection
- [ ] Trench segmentation
- [ ] Compliance analysis
- [ ] **Expected Outputs**: Complete RES project results

### **Afternoon (13:00-17:00): Visualization Testing**
**Notebook**: `notebooks/visualization/pipeline_visualization.ipynb`

#### **Test 6.2: 3D Visualization Generation**
- [ ] Castro 3D point cloud overlay
- [ ] Mudjar interactive visualization
- [ ] Giorgio detection overlay
- [ ] **Expected Outputs**: `{Project}_3d_point_cloud_overlay.html`

#### **Test 6.3: Heatmap Generation**
- [ ] Point density heatmaps for all projects
- [ ] Confidence heatmaps for detection results
- [ ] **Expected Outputs**: `{Project}_point_density_heatmap.html`, `{Project}_confidence_heatmap.html`

#### **Test 6.4: Summary Dashboards**
- [ ] Pipeline summary for each project
- [ ] Cross-project comparison dashboard
- [ ] **Expected Outputs**: `{Project}_pipeline_summary.html`

**📝 Deliverables**: Complete visualization suite for all 6 projects

---

## 📋 **Day 7: Integration Testing & Documentation**

### **Morning (9:00-12:00): End-to-End Integration**

#### **Test 7.1: Complete Pipeline Validation**
- [ ] Run complete pipeline on all 6 projects
- [ ] Validate data flow between stages
- [ ] Check file format compatibility
- [ ] Verify output file integrity

#### **Test 7.2: Performance Benchmarking**
- [ ] Document processing times per stage
- [ ] Memory usage analysis
- [ ] File size comparisons
- [ ] Quality metrics compilation

### **Afternoon (13:00-17:00): Documentation & Reporting**

#### **Test 7.3: Results Documentation**
- [ ] Create project-specific result summaries
- [ ] Document any issues encountered
- [ ] Performance comparison tables
- [ ] Quality assessment reports

#### **Test 7.4: Final Validation**
- [ ] Cross-check all output files
- [ ] Validate JSON/CSV formats
- [ ] Test visualization file accessibility
- [ ] Prepare deployment package

**📝 Deliverables**: Complete testing report and validated pipeline

---

## 📊 **Expected Output Structure**

```
output/
├── ENEL/
│   ├── Castro/
│   │   ├── ground_segmentation/
│   │   ├── alignment/
│   │   ├── pile_detection/
│   │   ├── trench_segmentation/
│   │   ├── compliance_analysis/
│   │   └── visualization/
│   ├── Mudjar/
│   └── Giorgio/
└── USA/
    ├── McCarthy/
    ├── RPCS/
    └── RES/
```

## 🎯 **Success Criteria**

### **Technical Validation**
- [ ] All notebooks execute without errors
- [ ] Output files generated in correct formats
- [ ] Data flows correctly between pipeline stages
- [ ] Visualizations render properly

### **Quality Validation**
- [ ] Ground segmentation accuracy >85%
- [ ] Pile detection recall >80%
- [ ] Alignment accuracy <1m deviation
- [ ] Compliance analysis completeness >90%

### **Performance Validation**
- [ ] Processing time <2 hours per project
- [ ] Memory usage <16GB peak
- [ ] Output file sizes reasonable
- [ ] No memory leaks or crashes

## 📞 **Daily Check-ins**
- **9:00 AM**: Daily standup and task review
- **12:00 PM**: Morning progress check
- **5:00 PM**: Daily wrap-up and issue documentation
- **6:00 PM**: Prepare next day's data and tasks

## 🚨 **Risk Mitigation**
- **Data Access Issues**: Have backup download methods ready
- **Processing Failures**: Document errors and implement fallbacks
- **Performance Issues**: Monitor resource usage and optimize as needed
- **Quality Issues**: Implement validation checks at each stage

---

## 🔧 **Daily Execution Commands**

### **Day 1 Commands**
```bash
# Setup data directories
mkdir -p data/ENEL/{Castro,Mudjar,Giorgio}/raw
mkdir -p data/USA/{McCarthy,RPCS,RES}/raw
mkdir -p output/ENEL/{Castro,Mudjar,Giorgio}
mkdir -p output/USA/{McCarthy,RPCS,RES}

# Download Castro data
aws s3 sync s3://preetam-filezilla-test/Castro/Pointcloud/ data/ENEL/Castro/raw/
aws s3 sync s3://preetam-filezilla-test/Castro/Ortho/ data/ENEL/Castro/raw/

# Download Mudjar data
wget -P data/ENEL/Mudjar/raw/ "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las"

# Test ground segmentation
cd notebooks/ground_segmentation/
# Set PROJECT_TYPE="ENEL", PROJECT_NAME="Castro"
jupyter nbconvert --execute ground_removal.ipynb
# Set PROJECT_TYPE="ENEL", PROJECT_NAME="Mudjar"
jupyter nbconvert --execute ground_removal.ipynb
```

### **Day 2 Commands**
```bash
# Download CAD data
aws s3 sync s3://ftp-enel/montalto_di_castro-italy/2025-01-30/CAD/ data/ENEL/Castro/raw/
aws s3 sync s3://ftp-enel/mudejar-spain/ data/ENEL/Mudjar/raw/

# Test alignment
cd notebooks/alignment/
# Set PROJECT_TYPE="ENEL", PROJECT_NAME="Castro"
jupyter nbconvert --execute ifc_pc_alignment_research.ipynb
# Set PROJECT_TYPE="ENEL", PROJECT_NAME="Mudjar"
jupyter nbconvert --execute ifc_pc_alignment_research.ipynb
```

### **Validation Commands**
```bash
# Check output files exist
ls -la output/ENEL/Castro/ground_segmentation/
ls -la output/ENEL/Castro/alignment/
ls -la output/ENEL/Castro/pile_detection/

# Validate file formats
python3 -c "
import pandas as pd
import numpy as np
import open3d as o3d

# Check CSV format
df = pd.read_csv('output/ENEL/Castro/pile_detection/Castro_detected_pile_centers.csv')
print(f'Pile detection CSV: {len(df)} rows, columns: {list(df.columns)}')

# Check point cloud format
pcd = o3d.io.read_point_cloud('output/ENEL/Castro/ground_segmentation/Castro_non_ground_points.ply')
print(f'Point cloud: {len(pcd.points)} points')
"
```

## 📋 **Quality Validation Checklist**

### **Ground Segmentation Quality**
- [ ] Ground points ratio: 30-70% of total points
- [ ] Non-ground points contain structures
- [ ] No obvious misclassification in visualization
- [ ] Output files readable by Open3D/PCL

### **Alignment Quality**
- [ ] Transformation matrix is reasonable
- [ ] Aligned point cloud overlaps with CAD geometry
- [ ] No extreme scaling or rotation
- [ ] Coordinate system consistency

### **Pile Detection Quality**
- [ ] Detection count matches expected range
- [ ] Confidence scores >0.5 for most detections
- [ ] Pile types correctly classified
- [ ] Spatial distribution makes sense

### **Compliance Analysis Quality**
- [ ] Spatial deviations <2m for most piles
- [ ] Compliance rates >70% overall
- [ ] JSON structure is valid
- [ ] CSV sortable and complete

### **Visualization Quality**
- [ ] HTML files open in browser
- [ ] 3D interactions work smoothly
- [ ] PNG images are high resolution
- [ ] Heatmaps show meaningful patterns

## 📊 **Performance Benchmarks**

| Stage | Expected Time | Memory Usage | Output Size |
|-------|---------------|--------------|-------------|
| Ground Segmentation | 5-15 min | 2-8 GB | 50-200 MB |
| Alignment | 10-30 min | 4-12 GB | 100-500 MB |
| Pile Detection | 5-20 min | 2-6 GB | 1-10 MB |
| Trench Segmentation | 10-25 min | 3-10 GB | 10-100 MB |
| Compliance Analysis | 2-10 min | 1-4 GB | 1-5 MB |
| Visualization | 5-15 min | 2-8 GB | 10-50 MB |

## 🚨 **Troubleshooting Guide**

### **Common Issues & Solutions**

#### **Data Download Issues**
```bash
# If S3 access fails
aws configure list
aws s3 ls s3://bucket-name/ --region us-east-1

# If HTTPS download fails
curl -I "https://url-to-check"
wget --spider "https://url-to-check"
```

#### **Memory Issues**
```python
# Reduce point cloud size for testing
import numpy as np
import open3d as o3d

pcd = o3d.io.read_point_cloud("large_file.las")
# Downsample to 50%
indices = np.random.choice(len(pcd.points), len(pcd.points)//2, replace=False)
pcd_small = pcd.select_by_index(indices)
o3d.io.write_point_cloud("small_file.ply", pcd_small)
```

#### **Notebook Execution Issues**
```bash
# Clear notebook outputs
jupyter nbconvert --clear-output notebook.ipynb

# Execute with timeout
timeout 3600 jupyter nbconvert --execute notebook.ipynb

# Check for JSON errors
python3 -c "import json; json.load(open('notebook.ipynb'))"
```

## 📈 **Progress Tracking Template**

### **Daily Progress Log**
```
Date: ___________
Tester: ___________

Completed Tasks:
- [ ] Task 1
- [ ] Task 2
- [ ] Task 3

Issues Encountered:
1. Issue description
   - Solution attempted
   - Resolution status

Performance Notes:
- Processing time: _____ minutes
- Memory usage: _____ GB
- Output file sizes: _____ MB

Quality Assessment:
- Ground segmentation: Pass/Fail
- Alignment accuracy: Pass/Fail
- Detection quality: Pass/Fail

Next Day Preparation:
- Data to download: _____
- Notebooks to prepare: _____
- Issues to investigate: _____
```

---

**📧 Contact**: Testing team lead for daily updates and issue resolution.

**🎯 Success Metrics**:
- 6 projects fully processed
- All pipeline stages validated
- Performance benchmarks met
- Quality thresholds achieved
- Complete documentation delivered
