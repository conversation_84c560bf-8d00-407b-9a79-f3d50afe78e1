import os
import csv
import subprocess
import argparse
from tqdm import tqdm

RCLONE_S3_REMOTE = "datasee-s3"

def infer_gdrive_path(project_type, project_name, data_type, local_path):
    if local_path and local_path != data_type:
        return f"gdrive:data/{project_type}/{project_name}/{data_type}/{local_path}"
    else:
        return f"gdrive:data/{project_type}/{project_name}/{data_type}"

def sync_to_gdrive_from_csv(csv_file, filter_project=None, filter_type=None, max_size=None, skip_low_priority=False):
    with open(csv_file, newline='') as f:
        reader = csv.DictReader(f)
        rows = list(reader)

    filtered = []
    for row in rows:
        if filter_project and row["project_name"].strip().lower() != filter_project.lower():
            continue
        if filter_type and row["data_type"].strip().lower() != filter_type.lower():
            continue
        if skip_low_priority and row["priority"].strip().lower() == "low":
            continue
        if max_size and float(row["estimated_size_gb"]) > max_size:
            continue
        filtered.append(row)

    print(f"\nWill process {len(filtered)} of {len(rows)} entries after filters...\n")

    for idx, row in enumerate(tqdm(filtered, desc="Processing files")):
        url = row["url"].strip()
        project_type = row["project_type"].strip()
        project_name = row["project_name"].strip()
        data_type = row["data_type"].strip()
        source_type = row["source_type"].strip().lower()
        local_path = row["local_path"].strip()
        description = row["description"].strip()
        size_gb = float(row["estimated_size_gb"])

        print(f"\n[{idx}] Syncing {description} ({size_gb} GB)...")

        destination = infer_gdrive_path(project_type, project_name, data_type, local_path)

        if source_type == "s3":
            source = url.replace("s3://", f"{RCLONE_S3_REMOTE}:")
            print(f"    Copying {source} → {destination}")
            try:
                subprocess.run([
                    "rclone", "copy", source, destination,
                    "--progress", "--create-empty-src-dirs"
                ], check=True)
            except subprocess.CalledProcessError as e:
                print(f"    Failed: {e}")

        elif source_type == "https":
            filename = os.path.basename(url.split("?")[0])
            tmp_path = f"/tmp/{filename}"
            print(f"    Downloading {url} to {tmp_path}")
            try:
                subprocess.run(["curl", "-L", url, "-o", tmp_path], check=True)
                subprocess.run(["rclone", "copy", tmp_path, destination], check=True)
                os.remove(tmp_path)
            except Exception as e:
                print(f"    Error: {e}")

        else:
            print(f"    Unsupported source_type: {source_type}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Sync selected project data to Google Drive.")
    parser.add_argument("csv", help="CSV file with data sources")
    parser.add_argument("--project", help="Filter by project name (optional)")
    parser.add_argument("--type", help="Filter by data type (pointcloud/cad/ortho)")
    parser.add_argument("--max-size", type=float, help="Max estimated size in GB")
    parser.add_argument("--skip-low", action="store_true", help="Skip low priority entries")

    args = parser.parse_args()
    sync_to_gdrive_from_csv(
        args.csv,
        filter_project=args.project,
        filter_type=args.type,
        max_size=args.max_size,
        skip_low_priority=args.skip_low
    )

