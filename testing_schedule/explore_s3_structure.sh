#!/bin/bash
# S3 Structure Explorer for Thesis Data
# Use AWS CLI to understand folder structure before downloading

echo "🔍 S3 STRUCTURE EXPLORATION FOR THESIS"
echo "======================================"
echo "Exploring folder structure to cherry-pick essential files"

# Function to explore S3 bucket structure
explore_s3_folder() {
    local s3_path="$1"
    local description="$2"
    local max_depth="${3:-2}"
    
    echo ""
    echo "📁 $description"
    echo "   Path: $s3_path"
    echo "   ----------------------------------------"
    
    # List folders and get overview
    echo "   📊 Folder structure:"
    aws s3 ls "$s3_path" --recursive --human-readable --summarize | head -20
    
    echo ""
    echo "   📋 File types found:"
    aws s3 ls "$s3_path" --recursive | grep -o '\.[^.]*$' | sort | uniq -c | sort -nr
    
    echo ""
    echo "   📦 Large files (>100MB):"
    aws s3 ls "$s3_path" --recursive --human-readable | awk '$3 ~ /[0-9]+\.[0-9]+.GB/ || ($3 ~ /[5-9][0-9][0-9]\.[0-9]+.MB/) || ($3 ~ /[0-9][0-9][0-9][0-9]+\.[0-9]+.MB/)' | head -10
}

# Function to find specific file types
find_file_types() {
    local s3_path="$1"
    local description="$2"
    
    echo ""
    echo "🎯 THESIS-RELEVANT FILES in $description"
    echo "   ----------------------------------------"
    
    echo "   📍 Point Cloud files (.las, .laz, .pcd):"
    aws s3 ls "$s3_path" --recursive | grep -i '\.(las|laz|pcd)$' | head -5
    
    echo "   🗺️  GeoTIFF files (.tif, .tiff):"
    aws s3 ls "$s3_path" --recursive | grep -i '\.(tif|tiff|geotiff)$' | head -5
    
    echo "   📐 CAD files (.dwg, .dxf, .ifc):"
    aws s3 ls "$s3_path" --recursive | grep -i '\.(dwg|dxf|ifc)$' | head -5
    
    echo "   🗺️  KML files (.kml, .kmz):"
    aws s3 ls "$s3_path" --recursive | grep -i '\.(kml|kmz)$' | head -5
    
    echo "   📷 Image files (.jpg, .jpeg, .png, .tif):"
    aws s3 ls "$s3_path" --recursive | grep -i '\.(jpg|jpeg|png|tif)$' | head -5
}

# Function to get size estimates for specific files
estimate_download_size() {
    local s3_path="$1"
    local file_pattern="$2"
    local description="$3"
    
    echo ""
    echo "📊 SIZE ESTIMATE: $description"
    echo "   ----------------------------------------"
    
    local files=$(aws s3 ls "$s3_path" --recursive --human-readable | grep -i "$file_pattern")
    
    if [ -n "$files" ]; then
        echo "$files" | head -10
        echo ""
        echo "   💾 Total files matching pattern: $(echo "$files" | wc -l)"
        
        # Calculate approximate total size (rough estimate)
        local total_mb=$(echo "$files" | awk '{
            if ($3 ~ /GB/) {
                gsub(/GB/, "", $3); total += $3 * 1024
            } else if ($3 ~ /MB/) {
                gsub(/MB/, "", $3); total += $3
            }
        } END {print total}')
        
        if [ -n "$total_mb" ] && [ "$total_mb" != "" ]; then
            echo "   📦 Estimated total size: ${total_mb} MB ($(echo "scale=2; $total_mb/1024" | bc) GB)"
        fi
    else
        echo "   ❌ No files found matching pattern: $file_pattern"
    fi
}

# MUDJAR EXPLORATION
echo ""
echo "🎯 MUDJAR PROJECT EXPLORATION"
echo "============================================"

# Mudjar CAD folder (the one currently downloading 144GB)
MUDJAR_CAD="s3://ftp-enel/mudejar-spain/"
explore_s3_folder "$MUDJAR_CAD" "Mudjar CAD Folder"
find_file_types "$MUDJAR_CAD" "Mudjar CAD"

# Look for specific thesis-relevant files
echo ""
echo "🔍 MUDJAR THESIS-SPECIFIC FILE SEARCH"
echo "======================================"

estimate_download_size "$MUDJAR_CAD" "\.dwg$" "DWG CAD Files"
estimate_download_size "$MUDJAR_CAD" "\.las$" "Point Cloud Files"
estimate_download_size "$MUDJAR_CAD" "\.tif$" "GeoTIFF Files"
estimate_download_size "$MUDJAR_CAD" "\.kml$" "KML Files"

# CASTRO EXPLORATION (if time permits)
echo ""
echo "🎯 CASTRO PROJECT EXPLORATION"
echo "============================================"

CASTRO_PC="s3://preetam-filezilla-test/Castro/Pointcloud/"
CASTRO_ORTHO="s3://preetam-filezilla-test/Castro/Ortho/"

explore_s3_folder "$CASTRO_PC" "Castro Point Cloud"
explore_s3_folder "$CASTRO_ORTHO" "Castro Orthomosaic"

# RECOMMENDATIONS
echo ""
echo "💡 THESIS DOWNLOAD RECOMMENDATIONS"
echo "=================================="
echo ""
echo "Based on exploration above:"
echo "1. 📊 Identify smallest essential files for each project"
echo "2. 🎯 Download only thesis-critical file types"
echo "3. ⚡ Start with single files, not entire folders"
echo "4. 📝 Document what you find for thesis methodology"
echo ""
echo "🚀 NEXT STEPS:"
echo "1. Review the file listings above"
echo "2. Identify 2-3 specific files to download"
echo "3. Use 'aws s3 cp' for individual files"
echo "4. Start method development immediately"
