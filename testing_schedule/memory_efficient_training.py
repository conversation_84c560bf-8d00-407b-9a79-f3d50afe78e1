#!/usr/bin/env python3
"""
Memory-Efficient Training Pipeline for Large Point Clouds

This module provides utilities for training ML models on large point cloud datasets
without loading entire files into memory.
"""

import numpy as np
import laspy
from pathlib import Path
import json
import gc
from typing import Iterator, Tuple, List, Dict
import psutil
import time

class MemoryEfficientDataLoader:
    """Data loader that streams point cloud chunks for training"""
    
    def __init__(self, chunk_manifest_path: str, batch_size: int = 32, 
                 max_points_per_sample: int = 4096, shuffle: bool = True):
        """
        Initialize the data loader
        
        Args:
            chunk_manifest_path: Path to chunk manifest JSON file
            batch_size: Number of samples per batch
            max_points_per_sample: Maximum points per training sample
            shuffle: Whether to shuffle chunks
        """
        self.chunk_manifest_path = Path(chunk_manifest_path)
        self.batch_size = batch_size
        self.max_points_per_sample = max_points_per_sample
        self.shuffle = shuffle
        
        # Load manifest
        with open(chunk_manifest_path, 'r') as f:
            self.manifest = json.load(f)
        
        self.chunks = self.manifest['chunks']
        if shuffle:
            np.random.shuffle(self.chunks)
        
        print(f"📊 DataLoader initialized:")
        print(f"  Chunks: {len(self.chunks)}")
        print(f"  Batch size: {batch_size}")
        print(f"  Max points per sample: {max_points_per_sample}")
    
    def load_chunk_points(self, chunk_path: str) -> np.ndarray:
        """Load points from a chunk file"""
        try:
            las = laspy.read(chunk_path)
            points = np.column_stack([las.x, las.y, las.z])
            
            # Normalize coordinates (important for training)
            centroid = np.mean(points, axis=0)
            points = points - centroid
            scale = np.max(np.linalg.norm(points, axis=1))
            if scale > 0:
                points = points / scale
            
            return points
        except Exception as e:
            print(f"Error loading chunk {chunk_path}: {e}")
            return np.array([])
    
    def extract_training_samples(self, points: np.ndarray, 
                               samples_per_chunk: int = 4) -> List[np.ndarray]:
        """Extract multiple training samples from a chunk"""
        if len(points) < self.max_points_per_sample:
            # If chunk is small, pad or return as-is
            if len(points) > 100:  # Minimum viable sample size
                return [points]
            else:
                return []
        
        samples = []
        for _ in range(samples_per_chunk):
            # Random sampling
            indices = np.random.choice(len(points), 
                                     size=min(self.max_points_per_sample, len(points)), 
                                     replace=False)
            sample = points[indices]
            samples.append(sample)
        
        return samples
    
    def __iter__(self) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """Iterate over batches of training data"""
        batch_samples = []
        batch_labels = []
        
        for chunk in self.chunks:
            chunk_path = chunk['output_path']
            
            # Load chunk
            points = self.load_chunk_points(chunk_path)
            if len(points) == 0:
                continue
            
            # Extract training samples from chunk
            samples = self.extract_training_samples(points)
            
            for sample in samples:
                # Pad or truncate to fixed size
                if len(sample) < self.max_points_per_sample:
                    # Pad with zeros
                    padding = np.zeros((self.max_points_per_sample - len(sample), 3))
                    sample = np.vstack([sample, padding])
                else:
                    sample = sample[:self.max_points_per_sample]
                
                batch_samples.append(sample)
                # For now, dummy labels (replace with actual labels for supervised learning)
                batch_labels.append(0)
                
                # Yield batch when full
                if len(batch_samples) >= self.batch_size:
                    yield (np.array(batch_samples), np.array(batch_labels))
                    batch_samples = []
                    batch_labels = []
                    
                    # Force garbage collection to free memory
                    gc.collect()
        
        # Yield remaining samples
        if batch_samples:
            yield (np.array(batch_samples), np.array(batch_labels))

class MemoryMonitor:
    """Monitor memory usage during training"""
    
    def __init__(self, warning_threshold_gb: float = 24.0):
        self.warning_threshold_gb = warning_threshold_gb
        self.start_memory = self.get_memory_usage()
        
    def get_memory_usage(self) -> float:
        """Get current memory usage in GB"""
        return psutil.virtual_memory().used / (1024**3)
    
    def check_memory(self) -> Dict:
        """Check current memory status"""
        current_memory = self.get_memory_usage()
        available_memory = psutil.virtual_memory().available / (1024**3)
        
        status = {
            'current_gb': current_memory,
            'available_gb': available_memory,
            'used_since_start_gb': current_memory - self.start_memory,
            'warning': current_memory > self.warning_threshold_gb
        }
        
        if status['warning']:
            print(f"⚠️  Memory warning: {current_memory:.1f} GB used")
            
        return status
    
    def force_cleanup(self):
        """Force garbage collection and memory cleanup"""
        gc.collect()
        time.sleep(0.1)  # Brief pause for cleanup

def train_on_large_dataset(chunk_manifest_path: str, model_save_path: str,
                          epochs: int = 10, batch_size: int = 16):
    """
    Example training function for large datasets
    
    This is a template - replace with your actual model training code
    """
    
    print(f"🚀 Starting training on large dataset...")
    print(f"  Manifest: {chunk_manifest_path}")
    print(f"  Epochs: {epochs}")
    print(f"  Batch size: {batch_size}")
    
    # Initialize memory monitor
    memory_monitor = MemoryMonitor(warning_threshold_gb=24.0)
    
    # Initialize data loader
    data_loader = MemoryEfficientDataLoader(
        chunk_manifest_path=chunk_manifest_path,
        batch_size=batch_size,
        max_points_per_sample=4096,
        shuffle=True
    )
    
    # Training loop
    for epoch in range(epochs):
        print(f"\n📈 Epoch {epoch + 1}/{epochs}")
        
        batch_count = 0
        total_samples = 0
        
        for batch_data, batch_labels in data_loader:
            batch_count += 1
            total_samples += len(batch_data)
            
            # Your model training code here
            # model.train_on_batch(batch_data, batch_labels)
            
            # Memory monitoring
            if batch_count % 10 == 0:
                memory_status = memory_monitor.check_memory()
                print(f"  Batch {batch_count}: {len(batch_data)} samples, "
                      f"Memory: {memory_status['current_gb']:.1f} GB")
                
                if memory_status['warning']:
                    memory_monitor.force_cleanup()
        
        print(f"  Epoch complete: {total_samples} samples processed")
        
        # Save checkpoint
        checkpoint_path = f"{model_save_path}_epoch_{epoch+1}.pkl"
        # save_model(model, checkpoint_path)
        print(f"  Checkpoint saved: {checkpoint_path}")
    
    print(f"\n✅ Training complete!")

def analyze_training_feasibility(chunk_manifest_path: str) -> Dict:
    """Analyze if training is feasible with current hardware"""
    
    with open(chunk_manifest_path, 'r') as f:
        manifest = json.load(f)
    
    chunks = manifest['chunks']
    total_chunks = len(chunks)
    total_size_mb = sum(chunk['file_size_mb'] for chunk in chunks)
    
    # Estimate memory requirements
    avg_points_per_chunk = np.mean([chunk['final_points'] for chunk in chunks])
    max_points_per_chunk = max(chunk['final_points'] for chunk in chunks)
    
    # Memory estimates (rough)
    memory_per_point_bytes = 12  # 3 floats (x,y,z) * 4 bytes each
    max_chunk_memory_gb = (max_points_per_chunk * memory_per_point_bytes) / (1024**3)
    
    # System info
    system_memory_gb = psutil.virtual_memory().total / (1024**3)
    available_memory_gb = psutil.virtual_memory().available / (1024**3)
    
    analysis = {
        'dataset_info': {
            'total_chunks': total_chunks,
            'total_size_mb': total_size_mb,
            'avg_points_per_chunk': int(avg_points_per_chunk),
            'max_points_per_chunk': max_points_per_chunk
        },
        'memory_estimates': {
            'max_chunk_memory_gb': max_chunk_memory_gb,
            'recommended_batch_size': max(1, int(8.0 / max_chunk_memory_gb)),  # Target 8GB per batch
            'system_memory_gb': system_memory_gb,
            'available_memory_gb': available_memory_gb
        },
        'feasibility': {
            'can_process_chunks': max_chunk_memory_gb < available_memory_gb * 0.8,
            'recommended_approach': 'streaming' if max_chunk_memory_gb > 2.0 else 'batch_loading'
        }
    }
    
    return analysis

def main():
    """Example usage"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Memory-efficient training utilities")
    parser.add_argument("command", choices=['analyze', 'train'], 
                       help="Command to run")
    parser.add_argument("--manifest", required=True,
                       help="Path to chunk manifest JSON file")
    parser.add_argument("--model-path", default="model_checkpoint",
                       help="Path to save model checkpoints")
    parser.add_argument("--epochs", type=int, default=5,
                       help="Number of training epochs")
    parser.add_argument("--batch-size", type=int, default=16,
                       help="Batch size for training")
    
    args = parser.parse_args()
    
    if args.command == 'analyze':
        analysis = analyze_training_feasibility(args.manifest)
        print(json.dumps(analysis, indent=2))
        
    elif args.command == 'train':
        train_on_large_dataset(
            args.manifest,
            args.model_path,
            args.epochs,
            args.batch_size
        )

if __name__ == "__main__":
    main()
