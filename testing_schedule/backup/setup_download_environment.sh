#!/bin/bash

# =============================================================================
# Setup Script for Energy Inspection 3D Data Download Environment
# =============================================================================
# This script helps configure rclone, AWS CLI, and other dependencies
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() { echo -e "${GREEN}[INFO]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*"; }
log_debug() { echo -e "${BLUE}[DEBUG]${NC} $*"; }

# =============================================================================
# DEPENDENCY INSTALLATION
# =============================================================================

install_dependencies() {
    log_info "Installing required dependencies..."
    
    # Detect OS
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            # Ubuntu/Debian
            sudo apt-get update
            sudo apt-get install -y wget curl unzip
        elif command -v yum &> /dev/null; then
            # CentOS/RHEL
            sudo yum install -y wget curl unzip
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install wget curl
        else
            log_warn "Homebrew not found. Please install manually: wget, curl"
        fi
    fi
    
    # Install rclone
    if ! command -v rclone &> /dev/null; then
        log_info "Installing rclone..."
        curl https://rclone.org/install.sh | sudo bash
    else
        log_info "✅ rclone already installed: $(rclone version | head -1)"
    fi
    
    # Install AWS CLI
    if ! command -v aws &> /dev/null; then
        log_info "Installing AWS CLI..."
        if [[ "$OSTYPE" == "linux-gnu"* ]]; then
            curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
            unzip awscliv2.zip
            sudo ./aws/install
            rm -rf awscliv2.zip aws/
        elif [[ "$OSTYPE" == "darwin"* ]]; then
            curl "https://awscli.amazonaws.com/AWSCLIV2.pkg" -o "AWSCLIV2.pkg"
            sudo installer -pkg AWSCLIV2.pkg -target /
            rm AWSCLIV2.pkg
        fi
    else
        log_info "✅ AWS CLI already installed: $(aws --version)"
    fi
    
    log_info "✅ Dependencies installation completed"
}

# =============================================================================
# RCLONE CONFIGURATION
# =============================================================================

configure_rclone_gdrive() {
    log_info "Configuring rclone for Google Drive..."
    
    # Check if gdrive remote already exists
    if rclone listremotes | grep -q "^gdrive:$"; then
        log_info "✅ Google Drive remote 'gdrive' already configured"
        return 0
    fi
    
    log_info "Setting up Google Drive remote..."
    log_warn "You'll need to authenticate with Google Drive in your browser"
    
    # Interactive rclone config for Google Drive
    cat << EOF

=============================================================================
RCLONE GOOGLE DRIVE SETUP
=============================================================================

We'll now configure rclone to connect to your Google Drive.
Please follow these steps:

1. Choose 'n' for new remote
2. Enter name: gdrive
3. Choose Google Drive (usually option 15)
4. Leave client_id blank (press Enter)
5. Leave client_secret blank (press Enter)
6. Choose scope: 1 (Full access)
7. Leave root_folder_id blank (press Enter)
8. Leave service_account_file blank (press Enter)
9. Choose 'n' for advanced config
10. Choose 'y' for auto config (if on desktop) or 'n' (if on server)
11. Follow browser authentication
12. Choose 'y' to confirm

Press Enter to continue...
EOF
    read -r
    
    rclone config
    
    # Verify configuration
    if rclone listremotes | grep -q "^gdrive:$"; then
        log_info "✅ Google Drive configured successfully"
        
        # Test connection
        log_info "Testing Google Drive connection..."
        if rclone lsd gdrive: &> /dev/null; then
            log_info "✅ Google Drive connection test successful"
        else
            log_warn "⚠️ Google Drive connection test failed"
        fi
    else
        log_error "❌ Google Drive configuration failed"
        return 1
    fi
}

configure_rclone_s3() {
    log_info "Configuring rclone for S3 access..."
    
    # We'll create S3 remotes dynamically in the download script
    # This function provides guidance for manual setup if needed
    
    cat << EOF

=============================================================================
S3 CONFIGURATION NOTES
=============================================================================

The download script will automatically configure S3 remotes for each bucket.
However, you may need AWS credentials configured.

If you encounter S3 access issues, you can manually configure S3 remotes:

1. Run: rclone config
2. Choose 'n' for new remote
3. Enter name: s3-bucket-name
4. Choose Amazon S3 (usually option 5)
5. Choose AWS (option 1)
6. Enter your AWS Access Key ID
7. Enter your AWS Secret Access Key
8. Choose region (e.g., us-east-1)
9. Leave other options as default

EOF
}

# =============================================================================
# AWS CONFIGURATION
# =============================================================================

configure_aws() {
    log_info "Configuring AWS CLI..."
    
    if aws configure list &> /dev/null; then
        log_info "✅ AWS CLI already configured"
        aws configure list
        return 0
    fi
    
    log_info "AWS CLI needs to be configured for S3 access"
    
    cat << EOF

=============================================================================
AWS CONFIGURATION
=============================================================================

You'll need AWS credentials to access S3 buckets.
If you don't have them, contact your AWS administrator.

The script will now run 'aws configure' to set up credentials.

You'll need:
- AWS Access Key ID
- AWS Secret Access Key
- Default region (e.g., us-east-1)
- Default output format (json)

Press Enter to continue...
EOF
    read -r
    
    aws configure
    
    # Test AWS configuration
    log_info "Testing AWS configuration..."
    if aws sts get-caller-identity &> /dev/null; then
        log_info "✅ AWS configuration test successful"
    else
        log_warn "⚠️ AWS configuration test failed"
    fi
}

# =============================================================================
# ENVIRONMENT SETUP
# =============================================================================

setup_environment() {
    log_info "Setting up environment..."
    
    # Create necessary directories
    mkdir -p logs
    mkdir -p data
    mkdir -p output
    
    # Make scripts executable
    chmod +x download_project_data.sh
    
    # Check .env file
    if [[ ! -f ".env" ]]; then
        log_warn ".env file not found"
        log_info "Creating default .env file..."
        
        cat > .env << 'EOF'
# Basic configuration - edit as needed
GDRIVE_REMOTE="gdrive"
GDRIVE_BASE_DIR="energy-inspection-3d-data"
LOCAL_DATA_DIR="./data"
LOG_FILE="./logs/data_download.log"
PARALLEL_DOWNLOADS=4
SKIP_EXISTING="true"
DRY_RUN="false"
VERBOSE="false"
BACKUP_TO_GDRIVE="true"
EOF
        
        log_info "✅ Default .env file created. Please review and edit as needed."
    else
        log_info "✅ .env file already exists"
    fi
    
    log_info "✅ Environment setup completed"
}

# =============================================================================
# VERIFICATION
# =============================================================================

verify_setup() {
    log_info "Verifying setup..."
    
    local issues=()
    
    # Check dependencies
    local deps=("rclone" "wget" "curl" "aws")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            issues+=("$dep not installed")
        fi
    done
    
    # Check rclone Google Drive config
    if ! rclone listremotes | grep -q "^gdrive:$"; then
        issues+=("Google Drive not configured in rclone")
    fi
    
    # Check AWS config
    if ! aws configure list &> /dev/null; then
        issues+=("AWS CLI not configured")
    fi
    
    # Check .env file
    if [[ ! -f ".env" ]]; then
        issues+=(".env file missing")
    fi
    
    # Report results
    if [[ ${#issues[@]} -eq 0 ]]; then
        log_info "✅ All checks passed! Setup is complete."
        log_info ""
        log_info "Next steps:"
        log_info "1. Review and edit .env file if needed"
        log_info "2. Run: ./download_project_data.sh"
        log_info "3. Or test with: DRY_RUN=true ./download_project_data.sh"
    else
        log_error "❌ Setup issues found:"
        for issue in "${issues[@]}"; do
            log_error "  - $issue"
        done
        return 1
    fi
}

# =============================================================================
# MAIN MENU
# =============================================================================

show_menu() {
    cat << EOF

=============================================================================
Energy Inspection 3D - Download Environment Setup
=============================================================================

Choose an option:

1) Install all dependencies
2) Configure rclone for Google Drive
3) Configure AWS CLI
4) Setup environment (directories, .env)
5) Verify complete setup
6) Run full setup (all of the above)
7) Exit

EOF
}

main_menu() {
    while true; do
        show_menu
        read -p "Enter your choice (1-7): " choice
        
        case $choice in
            1)
                install_dependencies
                ;;
            2)
                configure_rclone_gdrive
                ;;
            3)
                configure_aws
                ;;
            4)
                setup_environment
                ;;
            5)
                verify_setup
                ;;
            6)
                log_info "Running full setup..."
                install_dependencies
                configure_rclone_gdrive
                configure_rclone_s3
                configure_aws
                setup_environment
                verify_setup
                ;;
            7)
                log_info "Exiting setup"
                exit 0
                ;;
            *)
                log_error "Invalid choice. Please enter 1-7."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Handle command line arguments
case "${1:-menu}" in
    "install")
        install_dependencies
        ;;
    "rclone")
        configure_rclone_gdrive
        ;;
    "aws")
        configure_aws
        ;;
    "env")
        setup_environment
        ;;
    "verify")
        verify_setup
        ;;
    "full")
        install_dependencies
        configure_rclone_gdrive
        configure_aws
        setup_environment
        verify_setup
        ;;
    "menu"|*)
        main_menu
        ;;
esac
