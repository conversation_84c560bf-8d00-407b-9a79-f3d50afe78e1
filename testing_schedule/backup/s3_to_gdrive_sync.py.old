import csv
import os
import subprocess
import argparse
from tqdm import tqdm

def infer_gdrive_path(project_type, project_name, data_type):
    return f"gdrive:data/{project_type}/{project_name}/{data_type}"

def sync_to_gdrive_from_csv(csv_file):
    with open(csv_file, newline='') as f:
        reader = csv.DictReader(f)
        rows = list(reader)

    print(f"Processing {len(rows)} entries from {csv_file}...\n")

    for idx, row in enumerate(tqdm(rows, desc="Processing files")):
        url = row.get('url', '').strip()
        project_type = row.get('project_type', '').strip()
        project_name = row.get('project_name', '').strip()
        data_type = row.get('data_type', '').strip()

        if not url or not all([project_type, project_name, data_type]):
            print(f"[{idx}] Skipping entry due to missing fields.")
            continue

        destination = infer_gdrive_path(project_type, project_name, data_type)

        # S3 source
        if url.startswith("s3://"):
            source = url.replace("s3://", "datasee-s3:")
            print(f"[{idx}] Copying {source} → {destination}")
            try:
                subprocess.run([
                    "rclone", "copy", source, destination,
                    "--progress", "--create-empty-src-dirs"
                ], check=True)
            except subprocess.CalledProcessError as e:
                print(f"[{idx}] Failed to copy from S3: {e}")

        # HTTPS download (single file)
        elif url.startswith("http"):
            filename = os.path.basename(url).split("?")[0]
            target_path = os.path.join("/tmp", filename)

            print(f"[{idx}] Downloading from {url} to temporary path {target_path}")
            try:
                subprocess.run(["curl", "-L", url, "-o", target_path], check=True)
                subprocess.run(["rclone", "copy", target_path, destination], check=True)
                os.remove(target_path)
            except subprocess.CalledProcessError as e:
                print(f"[{idx}] Download or copy failed: {e}")
            except Exception as e:
                print(f"[{idx}] Unexpected error: {e}")

        else:
            print(f"[{idx}] Unsupported URL format: {url}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Sync remote data sources to Google Drive using rclone.")
    parser.add_argument("csv", help="Path to CSV file containing data sources")
    args = parser.parse_args()

    sync_to_gdrive_from_csv(args.csv)

