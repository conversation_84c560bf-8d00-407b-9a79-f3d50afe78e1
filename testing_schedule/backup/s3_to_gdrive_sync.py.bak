import csv
import subprocess
import argparse
from tqdm import tqdm

def s3_to_gdrive_from_csv(csv_file):
    with open(csv_file, newline='') as f:
        reader = csv.DictReader(f)
        rows = list(reader)

    tqdm.write(f"Processing {len(rows)} entries from {csv_file}...")

    for idx, row in enumerate(tqdm(rows, desc="Processing files")):
        url = row.get('url', '').strip()
        gdrive_path = row.get('gdrive_path', '').strip()

        if not url or not gdrive_path:
            tqdm.write(f"[{idx}] Missing URL or destination path.")
            continue

        if not url.startswith("s3://") or not gdrive_path.startswith("gdrive:"):
            tqdm.write(f"[{idx}] Skipping unsupported scheme in row: {url} → {gdrive_path}")
            continue

        source = url.replace("s3://", "s3:")
        tqdm.write(f"[{idx}] Downloading {source} → {gdrive_path}")

        try:
            result = subprocess.run(
                ["rclone", "copy", source, gdrive_path, "--progress"],
                check=True
            )
        except subprocess.CalledProcessError as e:
            tqdm.write(f"[{idx}] Failed to download: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Sync S3 to Google Drive using rclone and a CSV.")
    parser.add_argument("csv", help="Path to CSV with S3 URLs and Google Drive paths")
    args = parser.parse_args()
    s3_to_gdrive_from_csv(args.csv)

