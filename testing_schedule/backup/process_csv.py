import os
import pandas as pd
import argparse
import subprocess
from tqdm import tqdm

def process_csv(csv_file):
    df = pd.read_csv(csv_file)
    print(f"Processing {len(df)} entries from {csv_file}...\n")

    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing files"):
        url = str(row.get("url", "")).strip()
        local_path = str(row.get("local_path", "")).strip()

        if not url or not local_path:
            tqdm.write(f"[{idx}] Skipping: Missing URL or local path")
            continue

        is_remote = local_path.startswith("gdrive:")
        is_s3 = url.startswith("s3://")
        dest_path = local_path if is_remote else os.path.abspath(local_path)

        try:
            if is_remote:
                # Copy S3 to GDrive directly
                if is_s3:
                    cmd = ["rclone", "copy", url, dest_path]
                else:
                    cmd = ["rclone", "copyurl", url, dest_path, "--progress"]
            else:
                # Copy S3 to local, else use curl
                os.makedirs(os.path.dirname(dest_path), exist_ok=True)
                if is_s3:
                    cmd = ["rclone", "copy", url, dest_path]
                else:
                    cmd = ["curl", "-L", url, "-o", dest_path]

            tqdm.write(f"[{idx}] Downloading {url} → {dest_path}")
            subprocess.run(cmd, check=True)

        except Exception as e:
            tqdm.write(f"[{idx}] Failed to download: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download/copy files from CSV to local or GDrive")
    parser.add_argument("csv", help="Path to CSV file with project data")
    args = parser.parse_args()

    process_csv(args.csv)

