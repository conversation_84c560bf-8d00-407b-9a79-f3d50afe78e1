#!/usr/bin/env python3
"""
Thesis-Focused Data Download Strategy

Downloads only the essential datasets needed for 12-week thesis completion.
Prioritizes manageable datasets and avoids risky large files.
"""

import csv
import subprocess
import argparse
from pathlib import Path
import sys

# THESIS-SAFE DATASETS (confirmed manageable sizes)
THESIS_DATASETS = {
    'primary': [
        'Mudjar',    # 1.4 GB - Method development
    ],
    'validation': [
        'RPCS',      # 1.8 GB - Cross-validation  
        'McCarthy',  # 2.1 GB - Additional validation
    ],
    'optional': [
        'RES',       # 2.8 GB - Only if ahead of schedule
    ],
    'risky': [
        'Castro',    # 46.8 GB - TOO LARGE for thesis timeline
        'Giorgio',   # 62.7 GB - TOO LARGE for thesis timeline
    ]
}

def get_thesis_safe_datasets(csv_file: str, priority: str = 'primary') -> list:
    """Get datasets that are safe for thesis timeline"""
    
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
    
    safe_datasets = THESIS_DATASETS.get(priority, [])
    
    filtered_rows = []
    for row in rows:
        if row['project_name'] in safe_datasets:
            # Only include point cloud data for now (most critical)
            if row['data_type'] == 'pointcloud':
                filtered_rows.append(row)
    
    return filtered_rows

def download_thesis_datasets(csv_file: str, priority: str = 'primary', 
                           dry_run: bool = False) -> None:
    """Download datasets in priority order for thesis"""
    
    datasets = get_thesis_safe_datasets(csv_file, priority)
    
    if not datasets:
        print(f"No datasets found for priority: {priority}")
        return
    
    print(f"📚 THESIS-FOCUSED DOWNLOAD - Priority: {priority}")
    print(f"📊 Datasets to download: {len(datasets)}")
    
    total_size = sum(float(row['estimated_size_gb']) for row in datasets)
    print(f"📦 Total estimated size: {total_size:.1f} GB")
    
    if dry_run:
        print("\n🔍 DRY RUN - Would download:")
        for row in datasets:
            print(f"  - {row['project_name']}: {row['estimated_size_gb']} GB")
        return
    
    # Confirm download
    response = input(f"\nProceed with download? (y/N): ")
    if response.lower() != 'y':
        print("Download cancelled.")
        return
    
    # Download each dataset
    for i, row in enumerate(datasets, 1):
        project_name = row['project_name']
        size_gb = row['estimated_size_gb']
        url = row['url']
        
        print(f"\n[{i}/{len(datasets)}] Downloading {project_name} ({size_gb} GB)...")
        
        try:
            # Use the existing sync script
            cmd = [
                'python', 'sync_to_gdrive.py', csv_file,
                '--project', project_name,
                '--type', 'pointcloud'
            ]
            
            print(f"Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True)
            
            print(f"✅ {project_name} download completed")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ {project_name} download failed: {e}")
            
            # Ask if should continue
            response = input("Continue with next dataset? (y/N): ")
            if response.lower() != 'y':
                break

def validate_thesis_datasets(data_dir: str = "../data") -> None:
    """Validate that thesis datasets are properly downloaded"""
    
    data_path = Path(data_dir)
    
    print("🔍 Validating thesis datasets...")
    
    all_priorities = ['primary', 'validation', 'optional']
    
    for priority in all_priorities:
        datasets = THESIS_DATASETS[priority]
        
        print(f"\n📊 {priority.title()} datasets:")
        
        for project in datasets:
            # Check for ENEL or USA project structure
            enel_path = data_path / "ENEL" / project / "pointcloud"
            usa_path = data_path / "USA" / project / "pointcloud"
            
            found = False
            size_info = "Not found"
            
            for project_path in [enel_path, usa_path]:
                if project_path.exists():
                    # Count files and get size
                    las_files = list(project_path.glob("*.las")) + list(project_path.glob("*.laz"))
                    if las_files:
                        total_size = sum(f.stat().st_size for f in las_files) / (1024**3)
                        size_info = f"{len(las_files)} files, {total_size:.1f} GB"
                        found = True
                        break
            
            status = "✅" if found else "❌"
            print(f"  {status} {project}: {size_info}")
    
    # Summary
    print(f"\n📋 THESIS READINESS SUMMARY:")
    
    primary_ready = all(
        any((data_path / "ENEL" / p / "pointcloud").exists() or 
            (data_path / "USA" / p / "pointcloud").exists() 
            for p in THESIS_DATASETS['primary'])
    )
    
    validation_ready = any(
        any((data_path / "ENEL" / p / "pointcloud").exists() or 
            (data_path / "USA" / p / "pointcloud").exists() 
            for p in THESIS_DATASETS['validation'])
    )
    
    if primary_ready:
        print("✅ PRIMARY datasets ready - Can start method development")
    else:
        print("❌ PRIMARY datasets missing - Download Mudjar first")
    
    if validation_ready:
        print("✅ VALIDATION datasets ready - Can proceed with comparison")
    else:
        print("⚠️  VALIDATION datasets missing - Download RPCS or McCarthy")

def show_thesis_strategy():
    """Show the complete thesis dataset strategy"""
    
    print("📚 THESIS DATASET STRATEGY (12 weeks remaining)")
    print("=" * 60)
    
    print("\n🎯 PRIMARY (Week 4-5): Method Development")
    for project in THESIS_DATASETS['primary']:
        print(f"  ✅ {project} - Essential for all method implementation")
    
    print("\n🔬 VALIDATION (Week 6-8): Comparative Analysis")  
    for project in THESIS_DATASETS['validation']:
        print(f"  📊 {project} - Cross-validation and robustness testing")
    
    print("\n⭐ OPTIONAL (Week 8+): Additional Validation")
    for project in THESIS_DATASETS['optional']:
        print(f"  🔄 {project} - Only if ahead of schedule")
    
    print("\n❌ RISKY (Avoid for thesis): Too large for timeline")
    for project in THESIS_DATASETS['risky']:
        print(f"  ⚠️  {project} - Would require weeks of chunking implementation")
    
    print("\n💡 RECOMMENDATION:")
    print("1. Download Mudjar first (1.4 GB) - start method development")
    print("2. Download RPCS (1.8 GB) - for cross-validation")
    print("3. Download McCarthy (2.1 GB) - for additional validation")
    print("4. Skip Castro/Giorgio - too risky for thesis timeline")

def main():
    parser = argparse.ArgumentParser(description="Thesis-focused dataset download")
    parser.add_argument("command", 
                       choices=['download', 'validate', 'strategy', 'list'],
                       help="Command to run")
    parser.add_argument("--csv", default="data_sources.csv",
                       help="CSV file with data sources")
    parser.add_argument("--priority", 
                       choices=['primary', 'validation', 'optional'],
                       default='primary',
                       help="Dataset priority level")
    parser.add_argument("--data-dir", default="../data",
                       help="Data directory to validate")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be downloaded without downloading")
    
    args = parser.parse_args()
    
    if args.command == 'strategy':
        show_thesis_strategy()
        
    elif args.command == 'list':
        datasets = get_thesis_safe_datasets(args.csv, args.priority)
        print(f"📊 {args.priority.title()} datasets:")
        for row in datasets:
            print(f"  - {row['project_name']}: {row['estimated_size_gb']} GB")
            
    elif args.command == 'download':
        if not Path(args.csv).exists():
            print(f"Error: CSV file {args.csv} not found")
            sys.exit(1)
        download_thesis_datasets(args.csv, args.priority, args.dry_run)
        
    elif args.command == 'validate':
        validate_thesis_datasets(args.data_dir)

if __name__ == "__main__":
    main()
