#!/usr/bin/env python3
"""
Pipeline Testing Automation Script
Automates the 7-day testing schedule for the Energy Inspection 3D pipeline.
"""

import os
import sys
import json
import time
import subprocess
import logging
from pathlib import Path
from datetime import datetime, timedelta
import argparse

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pipeline_testing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PipelineTester:
    def __init__(self, config_file='project_configurations.json'):
        """Initialize the pipeline tester with project configurations."""
        self.config_file = Path(config_file)
        self.load_config()
        self.base_path = Path.cwd().parent  # Assume script is in testing_schedule/
        self.setup_directories()
        
    def load_config(self):
        """Load project configurations from JSON file."""
        try:
            with open(self.config_file, 'r') as f:
                self.config = json.load(f)
            logger.info(f"Loaded configuration from {self.config_file}")
        except Exception as e:
            logger.error(f"Failed to load config: {e}")
            sys.exit(1)
    
    def setup_directories(self):
        """Create necessary directory structure."""
        logger.info("Setting up directory structure...")
        
        # Create data directories
        for project_name, project_config in self.config['projects'].items():
            project_type = project_config['project_type']
            
            # Data directories
            data_dir = self.base_path / 'data' / project_type / project_name
            (data_dir / 'raw').mkdir(parents=True, exist_ok=True)
            (data_dir / 'preprocessing').mkdir(parents=True, exist_ok=True)
            (data_dir / 'ground_segmentation').mkdir(parents=True, exist_ok=True)
            (data_dir / 'alignment').mkdir(parents=True, exist_ok=True)
            
            # Output directories
            output_dir = self.base_path / 'output' / project_type / project_name
            (output_dir / 'ground_segmentation').mkdir(parents=True, exist_ok=True)
            (output_dir / 'alignment').mkdir(parents=True, exist_ok=True)
            (output_dir / 'pile_detection').mkdir(parents=True, exist_ok=True)
            (output_dir / 'trench_segmentation').mkdir(parents=True, exist_ok=True)
            (output_dir / 'compliance_analysis').mkdir(parents=True, exist_ok=True)
            (output_dir / 'visualization').mkdir(parents=True, exist_ok=True)
            
        logger.info("Directory structure created successfully")
    
    def download_data(self, project_name, data_type=None):
        """Download data for a specific project."""
        project_config = self.config['projects'][project_name]
        project_type = project_config['project_type']
        data_dir = self.base_path / 'data' / project_type / project_name / 'raw'
        
        logger.info(f"Downloading data for {project_name}...")
        
        data_sources = project_config['data_sources']
        
        for source_type, source_config in data_sources.items():
            if data_type and source_type != data_type:
                continue
                
            if not source_config.get('available', True):
                logger.info(f"Skipping {source_type} - not available")
                continue
                
            url = source_config['url']
            url_type = source_config['type']
            
            try:
                if url_type == 's3':
                    # AWS S3 download
                    cmd = f"aws s3 sync {url} {data_dir}/"
                    logger.info(f"Executing: {cmd}")
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                    
                elif url_type == 'https':
                    # HTTPS download
                    filename = url.split('/')[-1]
                    cmd = f"wget -P {data_dir}/ '{url}'"
                    logger.info(f"Executing: {cmd}")
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"Successfully downloaded {source_type} for {project_name}")
                else:
                    logger.error(f"Failed to download {source_type}: {result.stderr}")
                    
            except Exception as e:
                logger.error(f"Error downloading {source_type} for {project_name}: {e}")
    
    def run_notebook(self, notebook_path, project_type, project_name, timeout=3600):
        """Execute a Jupyter notebook with project configuration."""
        notebook_path = self.base_path / notebook_path
        
        if not notebook_path.exists():
            logger.error(f"Notebook not found: {notebook_path}")
            return False
        
        logger.info(f"Executing notebook: {notebook_path}")
        logger.info(f"Project: {project_type}/{project_name}")
        
        # Create a temporary notebook with project configuration
        temp_notebook = notebook_path.parent / f"temp_{project_name}_{notebook_path.name}"
        
        try:
            # Read original notebook
            with open(notebook_path, 'r') as f:
                notebook_content = f.read()
            
            # Replace project configuration variables
            notebook_content = notebook_content.replace(
                'PROJECT_TYPE = "ENEL"', 
                f'PROJECT_TYPE = "{project_type}"'
            )
            notebook_content = notebook_content.replace(
                'PROJECT_NAME = "Trino"', 
                f'PROJECT_NAME = "{project_name}"'
            )
            
            # Write temporary notebook
            with open(temp_notebook, 'w') as f:
                f.write(notebook_content)
            
            # Execute notebook
            cmd = f"jupyter nbconvert --execute --to notebook --inplace {temp_notebook}"
            
            start_time = time.time()
            result = subprocess.run(
                cmd, shell=True, capture_output=True, text=True, timeout=timeout
            )
            execution_time = time.time() - start_time
            
            if result.returncode == 0:
                logger.info(f"Notebook executed successfully in {execution_time:.1f}s")
                return True
            else:
                logger.error(f"Notebook execution failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error(f"Notebook execution timed out after {timeout}s")
            return False
        except Exception as e:
            logger.error(f"Error executing notebook: {e}")
            return False
        finally:
            # Clean up temporary notebook
            if temp_notebook.exists():
                temp_notebook.unlink()
    
    def validate_outputs(self, project_name, stage):
        """Validate outputs for a specific project and stage."""
        project_config = self.config['projects'][project_name]
        project_type = project_config['project_type']
        output_dir = self.base_path / 'output' / project_type / project_name / stage
        
        logger.info(f"Validating outputs for {project_name} - {stage}")
        
        stage_config = self.config['pipeline_stages'][stage]
        expected_formats = stage_config['output_formats']
        
        validation_passed = True
        
        for format_ext in expected_formats:
            # Look for files with expected format
            files = list(output_dir.glob(f"*.{format_ext}"))
            
            if not files:
                logger.warning(f"No {format_ext} files found in {output_dir}")
                validation_passed = False
            else:
                for file_path in files:
                    file_size = file_path.stat().st_size
                    logger.info(f"Found {file_path.name}: {file_size/1024/1024:.1f} MB")
                    
                    # Basic file validation
                    if file_size == 0:
                        logger.error(f"Empty file: {file_path}")
                        validation_passed = False
        
        # Stage-specific validation
        if stage == 'ground_segmentation':
            validation_passed &= self.validate_ground_segmentation(project_name)
        elif stage == 'pile_detection':
            validation_passed &= self.validate_pile_detection(project_name)
        
        return validation_passed
    
    def validate_ground_segmentation(self, project_name):
        """Validate ground segmentation results."""
        project_config = self.config['projects'][project_name]
        project_type = project_config['project_type']
        
        try:
            # Check if we can load the output point clouds
            import open3d as o3d
            
            output_dir = self.base_path / 'output' / project_type / project_name / 'ground_segmentation'
            
            non_ground_file = output_dir / f"{project_name}_non_ground_points.ply"
            ground_file = output_dir / f"{project_name}_ground_points.ply"
            
            if non_ground_file.exists():
                pcd = o3d.io.read_point_cloud(str(non_ground_file))
                num_points = len(pcd.points)
                logger.info(f"Non-ground points: {num_points:,}")
                
                # Check against validation criteria
                criteria = project_config['validation_criteria']['ground_segmentation']
                if num_points < criteria['min_points']:
                    logger.warning(f"Too few non-ground points: {num_points} < {criteria['min_points']}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Ground segmentation validation failed: {e}")
            return False
    
    def validate_pile_detection(self, project_name):
        """Validate pile detection results."""
        project_config = self.config['projects'][project_name]
        project_type = project_config['project_type']
        
        try:
            import pandas as pd
            
            output_dir = self.base_path / 'output' / project_type / project_name / 'pile_detection'
            csv_file = output_dir / f"{project_name}_detected_pile_centers.csv"
            
            if csv_file.exists():
                df = pd.read_csv(csv_file)
                num_detections = len(df)
                logger.info(f"Pile detections: {num_detections}")
                
                # Check against validation criteria
                criteria = project_config['validation_criteria']['pile_detection']
                expected_range = criteria['expected_pile_count_range']
                
                if not (expected_range[0] <= num_detections <= expected_range[1]):
                    logger.warning(f"Pile count outside expected range: {num_detections} not in {expected_range}")
                
                # Check confidence scores
                if 'confidence' in df.columns:
                    min_conf = df['confidence'].min()
                    avg_conf = df['confidence'].mean()
                    logger.info(f"Confidence scores - Min: {min_conf:.3f}, Avg: {avg_conf:.3f}")
                    
                    if min_conf < criteria['min_confidence']:
                        logger.warning(f"Low confidence detections found: {min_conf} < {criteria['min_confidence']}")
            
            return True
            
        except Exception as e:
            logger.error(f"Pile detection validation failed: {e}")
            return False
    
    def run_day_schedule(self, day_number):
        """Execute the testing schedule for a specific day."""
        logger.info(f"=== Starting Day {day_number} Testing ===")
        
        # Get projects scheduled for this day
        day_projects = []
        for project_name, project_config in self.config['projects'].items():
            if project_config['testing_schedule']['day'] == day_number:
                day_projects.append((project_name, project_config))
        
        if not day_projects:
            logger.info(f"No projects scheduled for day {day_number}")
            return True
        
        success = True
        
        for project_name, project_config in day_projects:
            logger.info(f"Processing project: {project_name}")
            
            # Download data if needed
            self.download_data(project_name)
            
            # Run scheduled stages
            stages = project_config['testing_schedule']['stages']
            project_type = project_config['project_type']
            
            for stage in stages:
                stage_config = self.config['pipeline_stages'][stage]
                notebook_path = stage_config['notebook']
                
                # Execute notebook
                notebook_success = self.run_notebook(
                    notebook_path, project_type, project_name
                )
                
                if notebook_success:
                    # Validate outputs
                    validation_success = self.validate_outputs(project_name, stage)
                    if not validation_success:
                        logger.error(f"Validation failed for {project_name} - {stage}")
                        success = False
                else:
                    logger.error(f"Notebook execution failed for {project_name} - {stage}")
                    success = False
        
        logger.info(f"=== Day {day_number} Testing Complete: {'SUCCESS' if success else 'FAILED'} ===")
        return success

def main():
    parser = argparse.ArgumentParser(description='Run pipeline testing schedule')
    parser.add_argument('--day', type=int, choices=range(1, 8), 
                       help='Run specific day (1-7)')
    parser.add_argument('--project', type=str, 
                       help='Run specific project only')
    parser.add_argument('--stage', type=str,
                       help='Run specific stage only')
    parser.add_argument('--download-only', action='store_true',
                       help='Only download data, do not run notebooks')
    
    args = parser.parse_args()
    
    tester = PipelineTester()
    
    if args.download_only:
        # Download data for all projects
        for project_name in tester.config['projects'].keys():
            tester.download_data(project_name)
        return
    
    if args.project and args.stage:
        # Run specific project and stage
        project_config = tester.config['projects'][args.project]
        project_type = project_config['project_type']
        stage_config = tester.config['pipeline_stages'][args.stage]
        
        success = tester.run_notebook(
            stage_config['notebook'], project_type, args.project
        )
        
        if success:
            tester.validate_outputs(args.project, args.stage)
    
    elif args.day:
        # Run specific day
        tester.run_day_schedule(args.day)
    
    else:
        # Run full 7-day schedule
        overall_success = True
        for day in range(1, 8):
            day_success = tester.run_day_schedule(day)
            overall_success &= day_success
            
            if not day_success:
                logger.error(f"Day {day} failed - stopping execution")
                break
        
        logger.info(f"=== 7-Day Testing Complete: {'SUCCESS' if overall_success else 'FAILED'} ===")

if __name__ == "__main__":
    main()
