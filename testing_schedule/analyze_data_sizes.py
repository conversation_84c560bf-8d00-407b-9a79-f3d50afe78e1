#!/usr/bin/env python3
"""
Data Size Analysis Tool for Energy Inspection 3D Project

This tool helps understand actual vs estimated data sizes across all projects
and provides recommendations for handling large datasets in ML pipelines.
"""

import csv
import subprocess
import json
import argparse
from pathlib import Path
from typing import Dict, List, Tuple
import sys

def get_s3_folder_size(s3_url: str, rclone_remote: str = "datasee-s3") -> Tuple[float, List[str]]:
    """Get actual size of S3 folder using rclone"""
    try:
        # Convert s3:// URL to rclone format
        rclone_path = s3_url.replace("s3://", f"{rclone_remote}:")
        
        # Get size using rclone
        result = subprocess.run([
            "rclone", "size", rclone_path, "--json"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            size_info = json.loads(result.stdout)
            size_gb = size_info.get("bytes", 0) / (1024**3)
            
            # Get file list
            file_result = subprocess.run([
                "rclone", "ls", rclone_path
            ], capture_output=True, text=True, timeout=60)
            
            files = []
            if file_result.returncode == 0:
                for line in file_result.stdout.strip().split('\n'):
                    if line.strip():
                        parts = line.strip().split(None, 1)
                        if len(parts) == 2:
                            file_size_bytes = int(parts[0])
                            file_name = parts[1]
                            file_size_gb = file_size_bytes / (1024**3)
                            files.append(f"{file_name}: {file_size_gb:.2f} GB")
            
            return size_gb, files
        else:
            print(f"Warning: Could not get size for {s3_url}: {result.stderr}")
            return 0.0, []
            
    except Exception as e:
        print(f"Error getting size for {s3_url}: {e}")
        return 0.0, []

def analyze_project_data(csv_file: str, check_actual_sizes: bool = False) -> Dict:
    """Analyze all project data sizes and provide ML training recommendations"""
    
    with open(csv_file, 'r') as f:
        reader = csv.DictReader(f)
        rows = list(reader)
    
    # Group by project
    projects = {}
    for row in rows:
        project_name = row['project_name']
        if project_name not in projects:
            projects[project_name] = {
                'project_type': row['project_type'],
                'data_sources': [],
                'total_estimated_gb': 0.0,
                'total_actual_gb': 0.0
            }
        
        estimated_size = float(row['estimated_size_gb'])
        projects[project_name]['total_estimated_gb'] += estimated_size
        
        data_source = {
            'data_type': row['data_type'],
            'source_type': row['source_type'],
            'url': row['url'],
            'estimated_size_gb': estimated_size,
            'actual_size_gb': 0.0,
            'files': []
        }
        
        # Get actual size if requested and it's S3
        if check_actual_sizes and row['source_type'].lower() == 's3':
            print(f"Checking actual size for {project_name} {row['data_type']}...")
            actual_size, files = get_s3_folder_size(row['url'])
            data_source['actual_size_gb'] = actual_size
            data_source['files'] = files
            projects[project_name]['total_actual_gb'] += actual_size
        
        projects[project_name]['data_sources'].append(data_source)
    
    return projects

def generate_ml_recommendations(projects: Dict) -> Dict:
    """Generate ML training recommendations based on data sizes"""
    
    recommendations = {
        'training_strategy': {},
        'data_processing': {},
        'infrastructure': {}
    }
    
    # Categorize projects by point cloud size
    small_projects = []    # < 5 GB
    medium_projects = []   # 5-15 GB  
    large_projects = []    # > 15 GB
    
    for project_name, project_data in projects.items():
        pc_size = 0.0
        for source in project_data['data_sources']:
            if source['data_type'] == 'pointcloud':
                pc_size = max(source['estimated_size_gb'], source['actual_size_gb'])
                break
        
        if pc_size < 5:
            small_projects.append((project_name, pc_size))
        elif pc_size < 15:
            medium_projects.append((project_name, pc_size))
        else:
            large_projects.append((project_name, pc_size))
    
    # Training strategy recommendations
    recommendations['training_strategy'] = {
        'phase_1_development': {
            'projects': small_projects,
            'description': 'Use for initial model development and validation',
            'memory_requirement': '8-16 GB RAM',
            'processing_time': '< 30 minutes per project'
        },
        'phase_2_validation': {
            'projects': medium_projects,
            'description': 'Use for method comparison and robustness testing',
            'memory_requirement': '16-32 GB RAM',
            'processing_time': '30-90 minutes per project'
        },
        'phase_3_scaling': {
            'projects': large_projects,
            'description': 'Use chunking/streaming for final validation',
            'memory_requirement': '32+ GB RAM or cloud processing',
            'processing_time': '2-6 hours per project'
        }
    }
    
    # Data processing recommendations
    recommendations['data_processing'] = {
        'chunking_strategy': {
            'large_datasets': [p[0] for p in large_projects],
            'chunk_size_gb': 2.0,
            'overlap_percentage': 10,
            'spatial_tiling': True
        },
        'downsampling': {
            'training_voxel_size': 0.1,  # Aggressive for training
            'validation_voxel_size': 0.05,  # More detailed for validation
            'inference_voxel_size': 0.02   # High detail for final results
        },
        'preprocessing_pipeline': [
            'Load raw point cloud',
            'Apply spatial chunking (if > 5GB)',
            'Downsample using voxel grid',
            'Remove statistical outliers',
            'Extract training patches (50m x 50m)',
            'Normalize coordinates'
        ]
    }
    
    return recommendations

def print_analysis_report(projects: Dict, recommendations: Dict):
    """Print comprehensive analysis report"""
    
    print("\n" + "="*80)
    print("ENERGY INSPECTION 3D - DATA SIZE ANALYSIS REPORT")
    print("="*80)
    
    # Project overview
    print(f"\n📊 PROJECT OVERVIEW")
    print("-" * 40)
    
    total_estimated = 0.0
    total_actual = 0.0
    
    for project_name, project_data in projects.items():
        estimated = project_data['total_estimated_gb']
        actual = project_data['total_actual_gb']
        total_estimated += estimated
        total_actual += actual
        
        print(f"\n{project_name} ({project_data['project_type']}):")
        print(f"  Estimated: {estimated:.1f} GB")
        if actual > 0:
            print(f"  Actual: {actual:.1f} GB")
            print(f"  Difference: {actual - estimated:+.1f} GB")
        
        # Show point cloud details
        for source in project_data['data_sources']:
            if source['data_type'] == 'pointcloud':
                print(f"  Point Cloud: {source['estimated_size_gb']:.1f} GB estimated", end="")
                if source['actual_size_gb'] > 0:
                    print(f", {source['actual_size_gb']:.1f} GB actual")
                    if source['files']:
                        print(f"    Files: {len(source['files'])}")
                        for file_info in source['files'][:3]:  # Show first 3 files
                            print(f"      - {file_info}")
                        if len(source['files']) > 3:
                            print(f"      ... and {len(source['files']) - 3} more files")
                else:
                    print()
    
    print(f"\n📦 TOTAL DATA:")
    print(f"  Estimated: {total_estimated:.1f} GB")
    if total_actual > 0:
        print(f"  Actual: {total_actual:.1f} GB")
    
    # ML Training Recommendations
    print(f"\n🤖 ML TRAINING STRATEGY RECOMMENDATIONS")
    print("-" * 50)
    
    for phase, details in recommendations['training_strategy'].items():
        print(f"\n{phase.replace('_', ' ').title()}:")
        print(f"  Projects: {[p[0] for p in details['projects']]}")
        print(f"  Description: {details['description']}")
        print(f"  Memory: {details['memory_requirement']}")
        print(f"  Time: {details['processing_time']}")
    
    # Data Processing Recommendations
    print(f"\n⚙️  DATA PROCESSING RECOMMENDATIONS")
    print("-" * 45)
    
    chunking = recommendations['data_processing']['chunking_strategy']
    print(f"\nChunking Strategy:")
    print(f"  Large datasets requiring chunking: {chunking['large_datasets']}")
    print(f"  Recommended chunk size: {chunking['chunk_size_gb']} GB")
    print(f"  Overlap: {chunking['overlap_percentage']}%")
    
    downsampling = recommendations['data_processing']['downsampling']
    print(f"\nDownsampling Strategy:")
    print(f"  Training: {downsampling['training_voxel_size']}m voxel size")
    print(f"  Validation: {downsampling['validation_voxel_size']}m voxel size")
    print(f"  Inference: {downsampling['inference_voxel_size']}m voxel size")
    
    print(f"\nProcessing Pipeline:")
    for i, step in enumerate(recommendations['data_processing']['preprocessing_pipeline'], 1):
        print(f"  {i}. {step}")

def main():
    parser = argparse.ArgumentParser(description="Analyze data sizes for ML training planning")
    parser.add_argument("csv_file", help="CSV file with data sources")
    parser.add_argument("--check-actual", action="store_true", 
                       help="Check actual sizes using rclone (slower)")
    parser.add_argument("--project", help="Analyze specific project only")
    
    args = parser.parse_args()
    
    if not Path(args.csv_file).exists():
        print(f"Error: CSV file {args.csv_file} not found")
        sys.exit(1)
    
    print("🔍 Analyzing project data sizes...")
    projects = analyze_project_data(args.csv_file, args.check_actual)
    
    if args.project:
        if args.project in projects:
            projects = {args.project: projects[args.project]}
        else:
            print(f"Error: Project {args.project} not found")
            sys.exit(1)
    
    recommendations = generate_ml_recommendations(projects)
    print_analysis_report(projects, recommendations)
    
    print(f"\n💡 NEXT STEPS:")
    print("1. Start with small projects for initial development")
    print("2. Implement chunking for Castro (46.8 GB)")
    print("3. Use aggressive downsampling for training phase")
    print("4. Consider cloud processing for large datasets")

if __name__ == "__main__":
    main()
