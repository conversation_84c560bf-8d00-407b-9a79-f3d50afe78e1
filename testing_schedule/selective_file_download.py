#!/usr/bin/env python3
"""
Selective File Download for <PERSON>sis

Cherry-pick only essential files instead of downloading entire folders.
Focus on point cloud files and skip unnecessary CAD/documentation.
"""

import subprocess
import json
import argparse
from pathlib import Path
import re

def list_s3_contents(s3_url: str, rclone_remote: str = "datasee-s3") -> list:
    """List contents of S3 folder to see what's available"""
    try:
        rclone_path = s3_url.replace("s3://", f"{rclone_remote}:")
        
        result = subprocess.run([
            "rclone", "ls", rclone_path, "--max-depth", "3"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            files = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.strip().split(None, 1)
                    if len(parts) == 2:
                        size_bytes = int(parts[0])
                        file_path = parts[1]
                        size_mb = size_bytes / (1024**2)
                        files.append({
                            'path': file_path,
                            'size_mb': size_mb,
                            'size_bytes': size_bytes
                        })
            return files
        else:
            print(f"Error listing {s3_url}: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"Error listing {s3_url}: {e}")
        return []

def filter_essential_files(files: list, project_type: str = "pointcloud") -> list:
    """Filter files to only essential ones for thesis"""
    
    essential_files = []
    
    for file_info in files:
        file_path = file_info['path'].lower()
        size_mb = file_info['size_mb']
        
        # Skip very large files (> 5GB) for thesis safety
        if size_mb > 5000:
            continue
            
        if project_type == "pointcloud":
            # Point cloud files
            if any(ext in file_path for ext in ['.las', '.laz', '.pcd', '.ply']):
                # Prefer smaller point cloud files for thesis
                if size_mb < 2000:  # < 2GB per file
                    essential_files.append(file_info)
                    
        elif project_type == "cad":
            # CAD files - only essential formats
            if any(ext in file_path for ext in ['.dwg', '.dxf', '.ifc']):
                # Skip documentation and large archives
                if not any(skip in file_path for skip in ['doc', 'pdf', 'zip', 'archive']):
                    if size_mb < 500:  # < 500MB per CAD file
                        essential_files.append(file_info)
                        
        elif project_type == "ortho":
            # Orthomosaic files
            if any(ext in file_path for ext in ['.tif', '.tiff', '.geotiff']):
                if size_mb < 3000:  # < 3GB per ortho file
                    essential_files.append(file_info)
    
    return essential_files

def download_selective_files(s3_url: str, essential_files: list, 
                           local_dir: str, rclone_remote: str = "datasee-s3") -> None:
    """Download only the selected essential files"""
    
    if not essential_files:
        print("No essential files found to download")
        return
    
    local_path = Path(local_dir)
    local_path.mkdir(parents=True, exist_ok=True)
    
    total_size_mb = sum(f['size_mb'] for f in essential_files)
    print(f"📦 Downloading {len(essential_files)} essential files ({total_size_mb:.1f} MB)")
    
    base_s3_path = s3_url.replace("s3://", f"{rclone_remote}:")
    
    for i, file_info in enumerate(essential_files, 1):
        file_path = file_info['path']
        size_mb = file_info['size_mb']
        
        print(f"\n[{i}/{len(essential_files)}] {file_path} ({size_mb:.1f} MB)")
        
        # Source and destination paths
        source_path = f"{base_s3_path}/{file_path}"
        dest_file = local_path / Path(file_path).name
        
        try:
            result = subprocess.run([
                "rclone", "copy", source_path, str(dest_file.parent),
                "--progress"
            ], check=True)
            
            print(f"✅ Downloaded: {dest_file.name}")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to download {file_path}: {e}")

def analyze_project_files_aws(project_name: str, s3_urls: dict) -> dict:
    """Analyze what files are available using AWS CLI (more reliable)"""

    analysis = {
        'project': project_name,
        'data_types': {}
    }

    for data_type, s3_url in s3_urls.items():
        print(f"\n🔍 Analyzing {project_name} {data_type} using AWS CLI...")

        try:
            # Use AWS CLI to list files
            result = subprocess.run([
                'aws', 's3', 'ls', s3_url, '--recursive', '--human-readable'
            ], capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                files = parse_aws_s3_output(result.stdout, data_type)
                essential_files = filter_essential_files_aws(files, data_type)

                total_size_mb = sum(f['size_mb'] for f in files)
                essential_size_mb = sum(f['size_mb'] for f in essential_files)

                analysis['data_types'][data_type] = {
                    'total_files': len(files),
                    'total_size_mb': total_size_mb,
                    'essential_files': len(essential_files),
                    'essential_size_mb': essential_size_mb,
                    'reduction_ratio': (total_size_mb - essential_size_mb) / total_size_mb if total_size_mb > 0 else 0,
                    'files': essential_files[:5],  # Show first 5 essential files
                    'file_types': get_file_type_distribution(files)
                }
            else:
                print(f"Error listing {s3_url}: {result.stderr}")
                analysis['data_types'][data_type] = {'error': result.stderr}

        except Exception as e:
            print(f"Error analyzing {s3_url}: {e}")
            analysis['data_types'][data_type] = {'error': str(e)}

    return analysis

def parse_aws_s3_output(aws_output: str, data_type: str) -> list:
    """Parse AWS S3 ls output into file list"""
    files = []
    for line in aws_output.strip().split('\n'):
        if line.strip():
            parts = line.strip().split()
            if len(parts) >= 4:
                # Parse size (handle different units)
                size_str = parts[2]
                if 'GiB' in size_str:
                    size_mb = float(size_str.replace('GiB', '')) * 1024
                elif 'MiB' in size_str:
                    size_mb = float(size_str.replace('MiB', ''))
                elif 'KiB' in size_str:
                    size_mb = float(size_str.replace('KiB', '')) / 1024
                else:
                    # Assume bytes
                    try:
                        size_mb = float(size_str) / (1024 * 1024)
                    except:
                        size_mb = 0

                file_path = ' '.join(parts[3:])
                files.append({
                    'path': file_path,
                    'size_mb': size_mb,
                    'date': parts[0],
                    'time': parts[1]
                })
    return files

def filter_essential_files_aws(files: list, project_type: str = "cad") -> list:
    """Filter files based on AWS CLI output for thesis needs"""
    essential_files = []

    for file_info in files:
        file_path = file_info['path'].lower()
        size_mb = file_info['size_mb']

        # Skip very large files for thesis safety
        if size_mb > 2000:  # 2GB limit
            continue

        if project_type == "cad":
            # Essential CAD files
            if any(ext in file_path for ext in ['.dwg', '.dxf', '.ifc']):
                if size_mb < 100:  # Reasonable CAD file size
                    essential_files.append(file_info)
            # KML files (always small and useful)
            elif any(ext in file_path for ext in ['.kml', '.kmz']):
                essential_files.append(file_info)
            # Skip RGB images for now (too many files)
            # Skip PDFs and ZIPs (documentation)

        elif project_type == "pointcloud":
            # Point cloud files
            if any(ext in file_path for ext in ['.las', '.laz', '.pcd', '.ply']):
                if size_mb < 2000:  # < 2GB per file
                    essential_files.append(file_info)

        elif project_type == "ortho":
            # Orthomosaic files
            if any(ext in file_path for ext in ['.tif', '.tiff', '.geotiff']):
                if size_mb < 1000:  # < 1GB per ortho file
                    essential_files.append(file_info)

    return essential_files

def get_file_type_distribution(files: list) -> dict:
    """Get distribution of file types"""
    type_counts = {}
    for file_info in files:
        ext = Path(file_info['path']).suffix.lower()
        type_counts[ext] = type_counts.get(ext, 0) + 1
    return dict(sorted(type_counts.items(), key=lambda x: x[1], reverse=True))

def cherry_pick_mudjar() -> None:
    """Cherry-pick essential files from Mudjar project based on real AWS data"""

    mudjar_urls = {
        'cad': 's3://ftp-enel/mudejar-spain/'
    }

    print("🎯 MUDJAR SELECTIVE DOWNLOAD ANALYSIS (Real AWS Data)")
    print("=" * 60)
    print("Based on your AWS CLI exploration:")
    print("- 27,602 .jpg files (RGB images)")
    print("- 1,604 .jpeg files")
    print("- 1 .dwg file (36.4 MB)")
    print("- 2 .kml files")
    print("- 1 .kmz file")
    print("- NO .las files found")

    # Analyze what's available
    analysis = analyze_project_files_aws('Mudjar', mudjar_urls)

    # Show analysis
    for data_type, info in analysis['data_types'].items():
        if 'error' in info:
            print(f"\n❌ {data_type.upper()}: {info['error']}")
            continue

        print(f"\n📊 {data_type.upper()}:")
        print(f"  Total files: {info['total_files']}")
        print(f"  Total size: {info['total_size_mb']:.1f} MB")
        print(f"  Essential files: {info['essential_files']}")
        print(f"  Essential size: {info['essential_size_mb']:.1f} MB")
        print(f"  Size reduction: {info['reduction_ratio']:.1%}")

        if 'file_types' in info:
            print(f"  File types: {info['file_types']}")

        if info.get('files'):
            print(f"  Essential files to download:")
            for file_info in info['files']:
                print(f"    - {file_info['path']} ({file_info['size_mb']:.1f} MB)")

    # Critical finding
    print(f"\n🚨 CRITICAL FINDING:")
    print(f"❌ NO POINT CLOUD (.las) files found in Mudjar CAD folder")
    print(f"✅ Only 1 DWG file (36.4 MB) + KML files available")
    print(f"📷 29,206 RGB images (likely drone photos, not processed point cloud)")

    # Recommendation
    print(f"\n💡 THESIS RECOMMENDATION:")
    print(f"1. ✅ Download the single DWG file (36.4 MB)")
    print(f"2. ✅ Download KML files (geolocation)")
    print(f"3. ❌ Skip RGB images (raw drone photos, not useful for thesis)")
    print(f"4. 🔍 Look for point cloud in the HTTPS URL from data_sources.csv")
    print(f"5. 🎯 Consider switching to RPCS or McCarthy if no point cloud found")

def download_mudjar_essentials(output_dir: str = "./mudjar_essentials") -> None:
    """Download only essential Mudjar files for thesis based on real AWS data"""

    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    print("🎯 DOWNLOADING MUDJAR ESSENTIALS FOR THESIS")
    print("=" * 50)

    # Download the single DWG file (36.4 MB)
    dwg_s3_path = "s3://ftp-enel/mudejar-spain/2024-10-22/CAD/gre.eec.d.21.es.p.11730.00.149.04.dwg"
    dwg_local_path = output_path / "cad" / "mudjar_essential.dwg"
    dwg_local_path.parent.mkdir(parents=True, exist_ok=True)

    print(f"\n📥 Downloading DWG file (36.4 MB)...")
    try:
        result = subprocess.run([
            'aws', 's3', 'cp', dwg_s3_path, str(dwg_local_path)
        ], check=True)
        print(f"✅ Downloaded: {dwg_local_path}")
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to download DWG: {e}")

    # Download KML files
    kml_files = [
        "s3://ftp-enel/mudejar-spain/2024-09-23/KML/psfv_-_mudéjar_1.kmz",
        "s3://ftp-enel/mudejar-spain/2024-10-23/KML/mudejar_ejemplo.kml"
    ]

    for i, kml_s3_path in enumerate(kml_files):
        kml_filename = f"mudjar_geo_{i+1}{Path(kml_s3_path).suffix}"
        kml_local_path = output_path / "kml" / kml_filename
        kml_local_path.parent.mkdir(parents=True, exist_ok=True)

        print(f"\n📥 Downloading KML file {i+1}...")
        try:
            result = subprocess.run([
                'aws', 's3', 'cp', kml_s3_path, str(kml_local_path)
            ], check=True)
            print(f"✅ Downloaded: {kml_local_path}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to download KML {i+1}: {e}")

    # Try to get point cloud from the HTTPS URL
    print(f"\n🔍 Checking for point cloud in HTTPS location...")
    pc_https_url = "https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/mudejar-spain_07-11-2024-pointcloud.las"
    pc_local_path = output_path / "pointcloud" / "mudejar_pointcloud.las"
    pc_local_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        result = subprocess.run([
            'curl', '-L', '--fail', '--progress-bar',
            '-o', str(pc_local_path),
            pc_https_url
        ], check=True)

        # Check if download was successful
        if pc_local_path.exists() and pc_local_path.stat().st_size > 1024*1024:  # > 1MB
            size_mb = pc_local_path.stat().st_size / (1024*1024)
            print(f"✅ Downloaded point cloud: {size_mb:.1f} MB")
        else:
            print(f"❌ Point cloud download failed or file too small")
            if pc_local_path.exists():
                pc_local_path.unlink()

    except subprocess.CalledProcessError as e:
        print(f"❌ Point cloud download failed: {e}")

    # Summary
    print(f"\n📋 MUDJAR DOWNLOAD SUMMARY:")
    downloaded_files = []
    total_size_mb = 0

    for file_path in [dwg_local_path, pc_local_path]:
        if file_path.exists():
            size_mb = file_path.stat().st_size / (1024*1024)
            downloaded_files.append(f"✅ {file_path.name}: {size_mb:.1f} MB")
            total_size_mb += size_mb
        else:
            downloaded_files.append(f"❌ {file_path.name}: Failed")

    for file_info in downloaded_files:
        print(f"   {file_info}")

    print(f"\n📦 Total downloaded: {total_size_mb:.1f} MB")

    if pc_local_path.exists():
        print(f"\n🚀 THESIS READY:")
        print(f"   ✅ Point cloud available for method development")
        print(f"   ✅ CAD reference for alignment")
        print(f"   ✅ Start implementing ICP, PointNet, ground segmentation")
    else:
        print(f"\n⚠️  THESIS RISK:")
        print(f"   ❌ No point cloud found - consider switching to RPCS/McCarthy")
        print(f"   🔄 Alternative: Use CAD for method development, add point cloud later")

def main():
    parser = argparse.ArgumentParser(description="Selective file download for thesis")
    parser.add_argument("command", 
                       choices=['analyze', 'download', 'mudjar-analyze', 'mudjar-download'],
                       help="Command to run")
    parser.add_argument("--project", help="Project name")
    parser.add_argument("--output-dir", default="./selective_downloads",
                       help="Output directory")
    
    args = parser.parse_args()
    
    if args.command == 'mudjar-analyze':
        cherry_pick_mudjar()
        
    elif args.command == 'mudjar-download':
        download_mudjar_essentials(args.output_dir)
        
    elif args.command == 'analyze':
        if not args.project:
            print("Error: --project required for analyze command")
            return
        print(f"Analysis for {args.project} not yet implemented")
        
    elif args.command == 'download':
        if not args.project:
            print("Error: --project required for download command")
            return
        print(f"Download for {args.project} not yet implemented")

if __name__ == "__main__":
    main()
