#!/usr/bin/env python3
"""
Selective File Download for <PERSON>sis

Cherry-pick only essential files instead of downloading entire folders.
Focus on point cloud files and skip unnecessary CAD/documentation.
"""

import subprocess
import json
import argparse
from pathlib import Path
import re

def list_s3_contents(s3_url: str, rclone_remote: str = "datasee-s3") -> list:
    """List contents of S3 folder to see what's available"""
    try:
        rclone_path = s3_url.replace("s3://", f"{rclone_remote}:")
        
        result = subprocess.run([
            "rclone", "ls", rclone_path, "--max-depth", "3"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            files = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    parts = line.strip().split(None, 1)
                    if len(parts) == 2:
                        size_bytes = int(parts[0])
                        file_path = parts[1]
                        size_mb = size_bytes / (1024**2)
                        files.append({
                            'path': file_path,
                            'size_mb': size_mb,
                            'size_bytes': size_bytes
                        })
            return files
        else:
            print(f"Error listing {s3_url}: {result.stderr}")
            return []
            
    except Exception as e:
        print(f"Error listing {s3_url}: {e}")
        return []

def filter_essential_files(files: list, project_type: str = "pointcloud") -> list:
    """Filter files to only essential ones for thesis"""
    
    essential_files = []
    
    for file_info in files:
        file_path = file_info['path'].lower()
        size_mb = file_info['size_mb']
        
        # Skip very large files (> 5GB) for thesis safety
        if size_mb > 5000:
            continue
            
        if project_type == "pointcloud":
            # Point cloud files
            if any(ext in file_path for ext in ['.las', '.laz', '.pcd', '.ply']):
                # Prefer smaller point cloud files for thesis
                if size_mb < 2000:  # < 2GB per file
                    essential_files.append(file_info)
                    
        elif project_type == "cad":
            # CAD files - only essential formats
            if any(ext in file_path for ext in ['.dwg', '.dxf', '.ifc']):
                # Skip documentation and large archives
                if not any(skip in file_path for skip in ['doc', 'pdf', 'zip', 'archive']):
                    if size_mb < 500:  # < 500MB per CAD file
                        essential_files.append(file_info)
                        
        elif project_type == "ortho":
            # Orthomosaic files
            if any(ext in file_path for ext in ['.tif', '.tiff', '.geotiff']):
                if size_mb < 3000:  # < 3GB per ortho file
                    essential_files.append(file_info)
    
    return essential_files

def download_selective_files(s3_url: str, essential_files: list, 
                           local_dir: str, rclone_remote: str = "datasee-s3") -> None:
    """Download only the selected essential files"""
    
    if not essential_files:
        print("No essential files found to download")
        return
    
    local_path = Path(local_dir)
    local_path.mkdir(parents=True, exist_ok=True)
    
    total_size_mb = sum(f['size_mb'] for f in essential_files)
    print(f"📦 Downloading {len(essential_files)} essential files ({total_size_mb:.1f} MB)")
    
    base_s3_path = s3_url.replace("s3://", f"{rclone_remote}:")
    
    for i, file_info in enumerate(essential_files, 1):
        file_path = file_info['path']
        size_mb = file_info['size_mb']
        
        print(f"\n[{i}/{len(essential_files)}] {file_path} ({size_mb:.1f} MB)")
        
        # Source and destination paths
        source_path = f"{base_s3_path}/{file_path}"
        dest_file = local_path / Path(file_path).name
        
        try:
            result = subprocess.run([
                "rclone", "copy", source_path, str(dest_file.parent),
                "--progress"
            ], check=True)
            
            print(f"✅ Downloaded: {dest_file.name}")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to download {file_path}: {e}")

def analyze_project_files(project_name: str, s3_urls: dict) -> dict:
    """Analyze what files are available for a project"""
    
    analysis = {
        'project': project_name,
        'data_types': {}
    }
    
    for data_type, s3_url in s3_urls.items():
        print(f"\n🔍 Analyzing {project_name} {data_type}...")
        
        files = list_s3_contents(s3_url)
        essential_files = filter_essential_files(files, data_type)
        
        total_size_mb = sum(f['size_mb'] for f in files) if files else 0
        essential_size_mb = sum(f['size_mb'] for f in essential_files)
        
        analysis['data_types'][data_type] = {
            'total_files': len(files),
            'total_size_mb': total_size_mb,
            'essential_files': len(essential_files),
            'essential_size_mb': essential_size_mb,
            'reduction_ratio': (total_size_mb - essential_size_mb) / total_size_mb if total_size_mb > 0 else 0,
            'files': essential_files[:5]  # Show first 5 essential files
        }
    
    return analysis

def cherry_pick_mudjar() -> None:
    """Cherry-pick essential files from Mudjar project"""
    
    mudjar_urls = {
        'pointcloud': 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/',
        'cad': 's3://ftp-enel/mudejar-spain/'
    }
    
    print("🎯 MUDJAR SELECTIVE DOWNLOAD ANALYSIS")
    print("=" * 50)
    
    # Analyze what's available
    analysis = analyze_project_files('Mudjar', mudjar_urls)
    
    # Show analysis
    for data_type, info in analysis['data_types'].items():
        print(f"\n📊 {data_type.upper()}:")
        print(f"  Total files: {info['total_files']}")
        print(f"  Total size: {info['total_size_mb']:.1f} MB")
        print(f"  Essential files: {info['essential_files']}")
        print(f"  Essential size: {info['essential_size_mb']:.1f} MB")
        print(f"  Size reduction: {info['reduction_ratio']:.1%}")
        
        if info['files']:
            print(f"  Sample files:")
            for file_info in info['files']:
                print(f"    - {file_info['path']} ({file_info['size_mb']:.1f} MB)")
    
    # Recommend download strategy
    print(f"\n💡 RECOMMENDATION:")
    
    pc_info = analysis['data_types'].get('pointcloud', {})
    if pc_info.get('essential_size_mb', 0) < 2000:
        print(f"✅ Point cloud: {pc_info.get('essential_size_mb', 0):.1f} MB - SAFE for thesis")
    else:
        print(f"⚠️  Point cloud: {pc_info.get('essential_size_mb', 0):.1f} MB - Consider further filtering")
    
    cad_info = analysis['data_types'].get('cad', {})
    if cad_info.get('essential_size_mb', 0) < 500:
        print(f"✅ CAD: {cad_info.get('essential_size_mb', 0):.1f} MB - SAFE for thesis")
    else:
        print(f"⚠️  CAD: {cad_info.get('essential_size_mb', 0):.1f} MB - Consider skipping for thesis")

def download_mudjar_essentials(output_dir: str = "./mudjar_essentials") -> None:
    """Download only essential Mudjar files for thesis"""
    
    # Focus on point cloud first (most critical for thesis)
    pointcloud_url = 'https://ftp-upload-images.s3.ap-south-1.amazonaws.com/Data+files+to+GIS+Team/CPM+%26+CQM/2024/ENEL/Mujedar/Fly1_07112024/'
    
    print("🎯 Downloading Mudjar point cloud essentials...")
    
    # List and filter point cloud files
    files = list_s3_contents(pointcloud_url)
    essential_files = filter_essential_files(files, 'pointcloud')
    
    if essential_files:
        pc_dir = Path(output_dir) / "pointcloud"
        download_selective_files(pointcloud_url, essential_files, str(pc_dir))
    else:
        print("❌ No suitable point cloud files found")

def main():
    parser = argparse.ArgumentParser(description="Selective file download for thesis")
    parser.add_argument("command", 
                       choices=['analyze', 'download', 'mudjar-analyze', 'mudjar-download'],
                       help="Command to run")
    parser.add_argument("--project", help="Project name")
    parser.add_argument("--output-dir", default="./selective_downloads",
                       help="Output directory")
    
    args = parser.parse_args()
    
    if args.command == 'mudjar-analyze':
        cherry_pick_mudjar()
        
    elif args.command == 'mudjar-download':
        download_mudjar_essentials(args.output_dir)
        
    elif args.command == 'analyze':
        if not args.project:
            print("Error: --project required for analyze command")
            return
        print(f"Analysis for {args.project} not yet implemented")
        
    elif args.command == 'download':
        if not args.project:
            print("Error: --project required for download command")
            return
        print(f"Download for {args.project} not yet implemented")

if __name__ == "__main__":
    main()
