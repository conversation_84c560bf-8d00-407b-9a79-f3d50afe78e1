#!/usr/bin/env python3
"""
Large Point Cloud Chunking Utility

This tool helps process large point cloud files (like Castro's 46GB dataset)
by splitting them into manageable spatial chunks for ML training.
"""

import numpy as np
import laspy
import argparse
from pathlib import Path
import json
from typing import List, Tu<PERSON>, Dict
import sys

def analyze_las_file(las_path: str) -> Dict:
    """Analyze LAS file without loading all points into memory"""
    try:
        with laspy.open(las_path) as las_file:
            header = las_file.header
            
            # Get bounds from header (fast)
            bounds = {
                'x_min': header.x_min,
                'x_max': header.x_max,
                'y_min': header.y_min,
                'y_max': header.y_max,
                'z_min': header.z_min,
                'z_max': header.z_max
            }
            
            # Calculate dimensions
            x_range = bounds['x_max'] - bounds['x_min']
            y_range = bounds['y_max'] - bounds['y_min']
            z_range = bounds['z_max'] - bounds['z_min']
            
            analysis = {
                'file_path': las_path,
                'point_count': header.point_count,
                'bounds': bounds,
                'dimensions': {
                    'x_range_m': x_range,
                    'y_range_m': y_range,
                    'z_range_m': z_range,
                    'area_m2': x_range * y_range
                },
                'point_density_per_m2': header.point_count / (x_range * y_range) if x_range * y_range > 0 else 0,
                'file_size_mb': Path(las_path).stat().st_size / (1024 * 1024)
            }
            
            return analysis
            
    except Exception as e:
        print(f"Error analyzing {las_path}: {e}")
        return None

def calculate_spatial_chunks(bounds: Dict, chunk_size_m: float = 100, overlap_m: float = 10) -> List[Dict]:
    """Calculate spatial chunk boundaries"""
    
    x_min, x_max = bounds['x_min'], bounds['x_max']
    y_min, y_max = bounds['y_min'], bounds['y_max']
    
    chunks = []
    chunk_id = 0
    
    # Calculate number of chunks needed
    x_range = x_max - x_min
    y_range = y_max - y_min
    
    x_chunks = int(np.ceil(x_range / chunk_size_m))
    y_chunks = int(np.ceil(y_range / chunk_size_m))
    
    print(f"Creating {x_chunks} x {y_chunks} = {x_chunks * y_chunks} spatial chunks")
    
    for i in range(x_chunks):
        for j in range(y_chunks):
            # Calculate chunk boundaries with overlap
            chunk_x_min = x_min + i * chunk_size_m - overlap_m
            chunk_x_max = x_min + (i + 1) * chunk_size_m + overlap_m
            chunk_y_min = y_min + j * chunk_size_m - overlap_m
            chunk_y_max = y_min + (j + 1) * chunk_size_m + overlap_m
            
            # Clamp to original bounds
            chunk_x_min = max(chunk_x_min, x_min)
            chunk_x_max = min(chunk_x_max, x_max)
            chunk_y_min = max(chunk_y_min, y_min)
            chunk_y_max = min(chunk_y_max, y_max)
            
            chunk = {
                'chunk_id': chunk_id,
                'grid_position': (i, j),
                'bounds': {
                    'x_min': chunk_x_min,
                    'x_max': chunk_x_max,
                    'y_min': chunk_y_min,
                    'y_max': chunk_y_max
                },
                'size_m': {
                    'x': chunk_x_max - chunk_x_min,
                    'y': chunk_y_max - chunk_y_min
                }
            }
            
            chunks.append(chunk)
            chunk_id += 1
    
    return chunks

def extract_chunk_from_las(las_path: str, chunk_bounds: Dict, output_path: str, 
                          downsample_voxel: float = 0.1) -> Dict:
    """Extract a spatial chunk from LAS file and save as smaller file"""
    
    try:
        # Read the full LAS file
        print(f"  Loading {las_path}...")
        las = laspy.read(las_path)
        
        # Filter points within chunk bounds
        x_mask = (las.x >= chunk_bounds['x_min']) & (las.x <= chunk_bounds['x_max'])
        y_mask = (las.y >= chunk_bounds['y_min']) & (las.y <= chunk_bounds['y_max'])
        chunk_mask = x_mask & y_mask
        
        chunk_points = np.sum(chunk_mask)
        
        if chunk_points == 0:
            print(f"    No points in chunk, skipping...")
            return None
        
        print(f"    Found {chunk_points:,} points in chunk")
        
        # Extract chunk data
        chunk_las = laspy.LasData(las.header)
        chunk_las.x = las.x[chunk_mask]
        chunk_las.y = las.y[chunk_mask]
        chunk_las.z = las.z[chunk_mask]
        
        # Copy other attributes if they exist
        if hasattr(las, 'intensity'):
            chunk_las.intensity = las.intensity[chunk_mask]
        if hasattr(las, 'classification'):
            chunk_las.classification = las.classification[chunk_mask]
        if hasattr(las, 'red'):
            chunk_las.red = las.red[chunk_mask]
            chunk_las.green = las.green[chunk_mask]
            chunk_las.blue = las.blue[chunk_mask]
        
        # Optional downsampling for training
        if downsample_voxel > 0 and chunk_points > 100000:  # Only downsample large chunks
            print(f"    Downsampling with {downsample_voxel}m voxel size...")
            # Simple voxel grid downsampling
            points = np.column_stack([chunk_las.x, chunk_las.y, chunk_las.z])
            voxel_indices = np.floor(points / downsample_voxel).astype(int)
            
            # Find unique voxels
            _, unique_indices = np.unique(voxel_indices, axis=0, return_index=True)
            
            # Keep only one point per voxel
            chunk_las.x = chunk_las.x[unique_indices]
            chunk_las.y = chunk_las.y[unique_indices]
            chunk_las.z = chunk_las.z[unique_indices]
            
            if hasattr(chunk_las, 'intensity'):
                chunk_las.intensity = chunk_las.intensity[unique_indices]
            if hasattr(chunk_las, 'classification'):
                chunk_las.classification = chunk_las.classification[unique_indices]
            
            downsampled_points = len(unique_indices)
            print(f"    Downsampled to {downsampled_points:,} points")
        
        # Save chunk
        chunk_las.write(output_path)
        
        # Return chunk info
        chunk_info = {
            'output_path': output_path,
            'original_points': chunk_points,
            'final_points': len(chunk_las.x),
            'bounds': chunk_bounds,
            'file_size_mb': Path(output_path).stat().st_size / (1024 * 1024)
        }
        
        print(f"    Saved chunk: {chunk_info['final_points']:,} points, {chunk_info['file_size_mb']:.1f} MB")
        
        return chunk_info
        
    except Exception as e:
        print(f"    Error extracting chunk: {e}")
        return None

def chunk_large_las_file(las_path: str, output_dir: str, chunk_size_m: float = 100, 
                        overlap_m: float = 10, downsample_voxel: float = 0.1,
                        max_chunks: int = None) -> List[Dict]:
    """Main function to chunk a large LAS file"""
    
    las_path = Path(las_path)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"🔍 Analyzing {las_path.name}...")
    
    # Analyze the file
    analysis = analyze_las_file(str(las_path))
    if not analysis:
        return []
    
    print(f"📊 File Analysis:")
    print(f"  Points: {analysis['point_count']:,}")
    print(f"  Size: {analysis['file_size_mb']:.1f} MB")
    print(f"  Area: {analysis['dimensions']['area_m2']:.0f} m²")
    print(f"  Density: {analysis['point_density_per_m2']:.1f} points/m²")
    
    # Calculate spatial chunks
    print(f"\n📐 Calculating spatial chunks ({chunk_size_m}m x {chunk_size_m}m)...")
    chunks = calculate_spatial_chunks(analysis['bounds'], chunk_size_m, overlap_m)
    
    if max_chunks:
        chunks = chunks[:max_chunks]
        print(f"  Limited to first {max_chunks} chunks for testing")
    
    # Extract chunks
    print(f"\n⚙️  Extracting {len(chunks)} chunks...")
    
    chunk_results = []
    base_name = las_path.stem
    
    for i, chunk in enumerate(chunks):
        chunk_filename = f"{base_name}_chunk_{chunk['chunk_id']:03d}.las"
        chunk_output_path = output_dir / chunk_filename
        
        print(f"\n[{i+1}/{len(chunks)}] Processing chunk {chunk['chunk_id']}...")
        
        chunk_result = extract_chunk_from_las(
            str(las_path), 
            chunk['bounds'], 
            str(chunk_output_path),
            downsample_voxel
        )
        
        if chunk_result:
            chunk_result.update(chunk)
            chunk_results.append(chunk_result)
    
    # Save chunk manifest
    manifest_path = output_dir / f"{base_name}_chunks_manifest.json"
    with open(manifest_path, 'w') as f:
        json.dump({
            'source_file': str(las_path),
            'analysis': analysis,
            'chunking_params': {
                'chunk_size_m': chunk_size_m,
                'overlap_m': overlap_m,
                'downsample_voxel': downsample_voxel
            },
            'chunks': chunk_results
        }, f, indent=2)
    
    print(f"\n✅ Chunking complete!")
    print(f"  Created {len(chunk_results)} chunks")
    print(f"  Total output size: {sum(c['file_size_mb'] for c in chunk_results):.1f} MB")
    print(f"  Manifest saved: {manifest_path}")
    
    return chunk_results

def main():
    parser = argparse.ArgumentParser(description="Chunk large point cloud files for ML training")
    parser.add_argument("las_file", help="Input LAS file path")
    parser.add_argument("output_dir", help="Output directory for chunks")
    parser.add_argument("--chunk-size", type=float, default=100, 
                       help="Chunk size in meters (default: 100)")
    parser.add_argument("--overlap", type=float, default=10,
                       help="Overlap between chunks in meters (default: 10)")
    parser.add_argument("--downsample", type=float, default=0.1,
                       help="Voxel size for downsampling (default: 0.1m, 0 to disable)")
    parser.add_argument("--max-chunks", type=int,
                       help="Maximum number of chunks to create (for testing)")
    parser.add_argument("--analyze-only", action="store_true",
                       help="Only analyze the file, don't create chunks")
    
    args = parser.parse_args()
    
    if not Path(args.las_file).exists():
        print(f"Error: File {args.las_file} not found")
        sys.exit(1)
    
    if args.analyze_only:
        analysis = analyze_las_file(args.las_file)
        if analysis:
            print(json.dumps(analysis, indent=2))
    else:
        chunk_large_las_file(
            args.las_file,
            args.output_dir,
            args.chunk_size,
            args.overlap,
            args.downsample,
            args.max_chunks
        )

if __name__ == "__main__":
    main()
